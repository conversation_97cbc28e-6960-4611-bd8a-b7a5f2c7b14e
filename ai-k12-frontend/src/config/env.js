// 环境配置文件

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getApiUrl() {
  // 开发环境
  if (import.meta.env.DEV) {
    return import.meta.env.VITE_API_URL || 'http://localhost:8081/api'
  }
  
  // 生产环境
  return import.meta.env.VITE_API_URL || window.location.origin
}

/**
 * 获取当前环境
 * @returns {string} 环境名称
 */
export function getEnvironment() {
  return import.meta.env.MODE || 'development'
}

/**
 * 是否为开发环境
 * @returns {boolean}
 */
export function isDevelopment() {
  return import.meta.env.DEV
}

/**
 * 是否为生产环境
 * @returns {boolean}
 */
export function isProduction() {
  return import.meta.env.PROD
}

/**
 * 应用配置
 */
export const appConfig = {
  // 应用名称
  name: 'Thinky K12 AI Assistant',
  
  // 版本
  version: '1.0.0',
  
  // API配置
  api: {
    baseUrl: getApiUrl(),
    timeout: 30000
  },
  
  // 语音配置
  speech: {
    // 默认语言
    defaultLanguage: 'zh-CN',
    
    // 支持的语言
    supportedLanguages: [
      { code: 'zh-CN', name: '中文（普通话）' },
      { code: 'en-US', name: 'English (US)' },
      { code: 'en-GB', name: 'English (UK)' }
    ],
    
    // 默认TTS设置
    tts: {
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0
    }
  },
  
  // 聊天配置
  chat: {
    // 最大消息长度
    maxMessageLength: 1000,
    
    // 消息历史保留数量
    maxHistoryMessages: 100,
    
    // 自动保存间隔（毫秒）
    autoSaveInterval: 30000
  },
  
  // 文件上传配置
  upload: {
    // 支持的图片格式
    supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    
    // 最大文件大小（字节）
    maxFileSize: 10 * 1024 * 1024, // 10MB
    
    // 图片压缩质量
    imageQuality: 0.8
  },
  
  // 科目配置
  subjects: [
    { id: 'math', name: '数学', icon: '🔢' },
    { id: 'physics', name: '物理', icon: '⚛️' },
    { id: 'chemistry', name: '化学', icon: '⚗️' },
    { id: 'biology', name: '生物', icon: '🧬' },
    { id: 'chinese', name: '语文', icon: '📝' },
    { id: 'english', name: '英语', icon: '🔤' },
    { id: 'history', name: '历史', icon: '📚' },
    { id: 'geography', name: '地理', icon: '🌏' },
    { id: 'politics', name: '政治', icon: '🏛️' },
    { id: 'science', name: '科学', icon: '🔬' }
  ],
  
  // 年级配置
  grades: [
    { id: 'grade1', name: '小学一年级', level: 'primary' },
    { id: 'grade2', name: '小学二年级', level: 'primary' },
    { id: 'grade3', name: '小学三年级', level: 'primary' },
    { id: 'grade4', name: '小学四年级', level: 'primary' },
    { id: 'grade5', name: '小学五年级', level: 'primary' },
    { id: 'grade6', name: '小学六年级', level: 'primary' },
    { id: 'grade7', name: '初中一年级', level: 'middle' },
    { id: 'grade8', name: '初中二年级', level: 'middle' },
    { id: 'grade9', name: '初中三年级', level: 'middle' },
    { id: 'grade10', name: '高中一年级', level: 'high' },
    { id: 'grade11', name: '高中二年级', level: 'high' },
    { id: 'grade12', name: '高中三年级', level: 'high' }
  ],
  
  // 主题配置
  themes: {
    light: {
      primary: '#667eea',
      secondary: '#764ba2',
      background: '#ffffff',
      surface: '#f8f9fa',
      text: '#333333'
    },
    dark: {
      primary: '#667eea',
      secondary: '#764ba2',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      text: '#ffffff'
    }
  }
}

// 环境变量
export const env = {
  NODE_ENV: import.meta.env.MODE,
  API_URL: import.meta.env.VITE_API_URL,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || appConfig.name,
  BUILD_TIME: import.meta.env.VITE_BUILD_TIME || new Date().toISOString()
}
