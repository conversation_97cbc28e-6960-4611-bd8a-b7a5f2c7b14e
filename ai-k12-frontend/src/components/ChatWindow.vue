<template>
  <div class="chat-window">
    <!-- 聊天容器 -->
    <div class="chat-container" :style="chatContainerStyle">
      <!-- 聊天背景装饰 -->
      <div class="chat-background">
        <img src="../assets/images/chat-bg-4c860f.png" alt="Chat Background" class="bg-image-left" />
        <img src="../assets/images/chat-bg-large.png" alt="Chat Background" class="bg-image-right" />
      </div>

      <!-- 语音和图片按钮 -->
      <div class="media-buttons">
        <button class="media-btn plus-btn" @click="showMoreOptions" title="更多选项">
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M8 2V14M2 8H14" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
        <button class="media-btn history-btn" @click="showHistory" title="历史记录">
          <svg width="16" height="16" viewBox="0 0 16 16">
            <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="2" fill="none"/>
            <path d="M8 4V8L10 10" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
        <button 
          class="media-btn voice-btn" 
          @mousedown="handleVoiceMouseDown" 
          @mouseup="handleVoiceMouseUp" 
          @touchstart="handleVoiceTouchStart"
          @touchend="handleVoiceTouchEnd"
          :class="{ active: isRecording }" 
          title="按住说话"
        >
          <svg width="16" height="16" viewBox="0 0 16 16">
            <path d="M8 2C9.1 2 10 2.9 10 4V8C10 9.1 9.1 10 8 10C6.9 10 6 9.1 6 8V4C6 2.9 6.9 2 8 2Z" fill="currentColor"/>
            <path d="M12 8C12 10.2 10.2 12 8 12C5.8 12 4 10.2 4 8" stroke="currentColor" stroke-width="2" fill="none"/>
            <path d="M8 12V14" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>
      </div>

      <!-- 输入提示文字 -->
      <div class="input-hint">
        <p>Type, snap, or say it — let's solve it together.</p>
      </div>

      <!-- 聊天输入区域 -->
      <div class="chat-input-container">
        <div class="input-wrapper">
          <!-- 图片预览 -->
          <div v-if="selectedImage" class="image-preview">
            <img :src="selectedImage" alt="Selected image" />
            <button class="remove-image" @click="removeImage">×</button>
          </div>

          <!-- 文本输入框 -->
          <textarea
            v-model="inputMessage"
            @keydown="handleKeyDown"
            @input="adjustHeight"
            ref="messageInput"
            :placeholder="placeholderText"
            :disabled="isRecording"
            rows="1"
            class="main-input"
          ></textarea>

          <!-- 发送按钮 -->
          <button 
            class="send-btn" 
            @click="sendMessage" 
            :disabled="!canSend || isLoading"
            :class="{ 'can-send': canSend && !isLoading, 'loading': isLoading }"
          >
            <svg width="16" height="16" viewBox="0 0 16 16">
              <path d="M2 8L14 2L10 8L14 14L2 8Z" fill="currentColor"/>
            </svg>
          </button>
        </div>

        <!-- 语音录制状态 -->
        <div v-if="isRecording" class="recording-status">
          <div class="recording-animation">
            <div class="pulse"></div>
          </div>
          <span>正在录音中... ({{ recordingDuration }}秒)</span>
          <button class="stop-recording" @click="stopRecording">停止</button>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-if="messages.length > 0" class="messages-list" ref="messagesList">
        <div 
          v-for="message in messages" 
          :key="message.id"
          class="message-item"
          :class="{ 'user-message': message.isUser, 'ai-message': !message.isUser }"
        >
          <div class="message-avatar">
            <div v-if="!message.isUser" class="ai-avatar">🤖</div>
            <div v-else class="user-avatar">👤</div>
          </div>
          
          <div class="message-content">
            <div class="message-bubble">
              <!-- 图片消息 -->
              <img v-if="message.image" :src="message.image" alt="Message image" class="message-image" />
              
              <!-- 语音消息 -->
              <div v-if="message.audio" class="audio-message">
                <button @click="playAudio(message.audio)" class="play-audio-btn">
                  {{ isPlaying === message.id ? '⏸️' : '▶️' }}
                </button>
                <span>语音消息</span>
              </div>
              
              <!-- 文本消息 -->
              <div v-if="message.text" class="message-text" v-html="formatMessage(message.text)"></div>
              
              <!-- 流式响应指示器 -->
              <div v-if="message.isStreaming" class="streaming-indicator">
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
            
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      type="file" 
      ref="fileInput" 
      @change="handleFileSelect" 
      accept="image/*" 
      style="display: none"
    />
  </div>
</template>

<script>
import { marked } from 'marked'
import { k12RagChatStream } from '../api/ragApi.js'
import { speechToText } from '../api/speechApi.js'

export default {
  name: 'ChatWindow',
  props: {
    subject: {
      type: String,
      default: ''
    },
    grade: {
      type: String,
      default: ''
    }
  },
  emits: ['send-message'],
  data() {
    return {
      inputMessage: '',
      selectedImage: null,
      isRecording: false,
      messages: [],
      isPlaying: null,
      mediaRecorder: null,
      audioChunks: [],
      memoryId: Date.now().toString(), // 生成唯一的会话ID
      currentEventSource: null, // 当前的SSE连接
      isLoading: false, // 加载状态
      recordingStartTime: null, // 录音开始时间
      recordingDuration: 0, // 录音时长
      recordingTimer: null // 录音计时器
    }
  },
  computed: {
    canSend() {
      return (this.inputMessage.trim() || this.selectedImage || this.isRecording) && !this.isLoading
    },
    
    placeholderText() {
      if (this.isRecording) {
        return `正在录音中... (${this.recordingDuration}秒)`
      }
      if (this.subject && this.grade) {
        return `向${this.grade}${this.subject}AI助手提问...`
      }
      return 'Type, snap, or say it — let\'s solve it together.'
    },
    
    chatContainerStyle() {
      return {
        borderRadius: '22px',
        backgroundColor: '#e5fbff'
      }
    }
  },
  methods: {
    showMoreOptions() {
      // 显示更多选项菜单（图片上传、文件上传等）
      this.selectImage()
    },
    
    showHistory() {
      // 显示聊天历史记录
      console.log('显示聊天历史')
      // 这里可以添加显示历史记录的逻辑
    },
    
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },
    
    adjustHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
          textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
        }
      })
    },
    
    sendMessage() {
      if (!this.canSend || this.isLoading) return
      
      const messageText = this.inputMessage.trim()
      if (!messageText) return
      
      const userMessage = {
        id: Date.now(),
        text: messageText,
        image: this.selectedImage,
        isUser: true,
        timestamp: new Date()
      }
      
      this.messages.push(userMessage)
      
      // 发送给父组件
      this.$emit('send-message', {
        text: userMessage.text,
        image: userMessage.image,
        subject: this.subject,
        grade: this.grade
      })
      
      // 清空输入
      this.inputMessage = ''
      this.selectedImage = null
      this.resetTextareaHeight()
      
      // 调用 RAG API
      this.sendToRAG(messageText)
      
      // 滚动到底部
      this.scrollToBottom()
    },
    
    sendToRAG(message) {
      // 关闭之前的连接
      if (this.currentEventSource) {
        this.currentEventSource.close()
      }
      
      this.isLoading = true
      
      // 创建AI响应消息
      const aiMessage = {
        id: Date.now() + 1,
        text: '',
        isUser: false,
        timestamp: new Date(),
        isStreaming: true
      }
      
      this.messages.push(aiMessage)
      this.scrollToBottom()
      
      // 调用RAG流式API
      this.currentEventSource = k12RagChatStream(
        this.memoryId,
        message,
        this.subject,
        this.grade,
        // onMessage 回调
        (chunk) => {
          console.log(`[ChatWindow] 收到消息块: [${chunk}]`)

          // 过滤掉流结束标识，避免显示给用户
          if (chunk === '[DONE]' || chunk.trim() === '[DONE]') {
            console.log('[ChatWindow] 收到流结束标识，忽略显示')
            return
          }

          console.log(`[ChatWindow] 添加内容到消息: [${chunk.substring(0, 50)}...]`)
          aiMessage.text += chunk
          this.$forceUpdate() // 强制更新视图
          this.scrollToBottom()
        },
        // onError 回调
        (error) => {
          console.error('[ChatWindow] RAG聊天错误:', error)
          console.log(`[ChatWindow] 当前消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)

          // 只有在没有收到任何内容时才显示错误消息
          if (!aiMessage.text || aiMessage.text.trim() === '') {
            console.log('[ChatWindow] 没有收到内容，显示错误消息')
            aiMessage.text = '抱歉，服务暂时不可用，请稍后重试。'
          } else {
            console.log('[ChatWindow] 已收到部分内容，保持现有消息不变')
          }
          aiMessage.isStreaming = false
          this.isLoading = false
          this.$forceUpdate()
        },
        // onClose 回调
        () => {
          console.log('[ChatWindow] RAG流式响应正常关闭')
          console.log(`[ChatWindow] 最终消息内容长度: ${aiMessage.text ? aiMessage.text.length : 0}`)
          aiMessage.isStreaming = false
          this.isLoading = false
          this.currentEventSource = null
          this.$forceUpdate()
          this.scrollToBottom()
        }
      )
    },
    
    selectImage() {
      this.$refs.fileInput.click()
    },
    
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          this.selectedImage = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    
    removeImage() {
      this.selectedImage = null
      this.$refs.fileInput.value = ''
    },
    
    // 按住录音功能
    async startRecording() {
      try {
        // 清除之前的计时器
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer)
        }
        
        // 获取麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        this.mediaRecorder = new MediaRecorder(stream)
        this.audioChunks = []
        
        // 开始计时
        this.recordingStartTime = Date.now()
        this.recordingDuration = 0
        this.recordingTimer = setInterval(() => {
          this.recordingDuration = Math.floor((Date.now() - this.recordingStartTime) / 1000)
        }, 1000)
        
        this.mediaRecorder.ondataavailable = (event) => {
          this.audioChunks.push(event.data)
        }
        
        this.mediaRecorder.onstop = async () => {
          // 停止计时
          if (this.recordingTimer) {
            clearInterval(this.recordingTimer)
            this.recordingTimer = null
          }
          
          // 创建音频文件
          const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' })
          
          try {
            // 显示转录状态
            this.isRecording = false
            this.isLoading = true
            
            // 调用ASR转写API
            const result = await speechToText(audioBlob, 'zh-CN')
            
            // 将转写结果填充到输入框
            if (result && result.text) {
              this.inputMessage = result.text
              // 自动发送消息
              this.sendMessage()
            }
          } catch (error) {
            console.error('语音转写失败:', error)
            alert('语音转写失败，请重试')
          } finally {
            this.isLoading = false
          }
          
          // 停止所有音轨
          stream.getTracks().forEach(track => track.stop())
        }
        
        this.mediaRecorder.start()
        this.isRecording = true
      } catch (error) {
        console.error('录音失败:', error)
        alert('无法访问麦克风，请检查权限设置')
        
        // 清除计时器
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer)
          this.recordingTimer = null
        }
        
        this.isRecording = false
      }
    },
    
    stopRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.mediaRecorder.stop()
        this.isRecording = false
        
        // 清除计时器
        if (this.recordingTimer) {
          clearInterval(this.recordingTimer)
          this.recordingTimer = null
        }
      }
    },
    
    // 鼠标事件处理
    handleVoiceMouseDown() {
      this.startRecording()
    },
    
    handleVoiceMouseUp() {
      this.stopRecording()
    },
    
    // 触摸事件处理（移动端支持）
    handleVoiceTouchStart() {
      this.startRecording()
    },
    
    handleVoiceTouchEnd() {
      this.stopRecording()
    },
    
    playAudio(audioUrl) {
      const audio = new Audio(audioUrl)
      audio.play()
    },
    
    formatMessage(text) {
      return marked(text, { breaks: true, gfm: true })
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    resetTextareaHeight() {
      this.$nextTick(() => {
        const textarea = this.$refs.messageInput
        if (textarea) {
          textarea.style.height = 'auto'
        }
      })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const messagesList = this.$refs.messagesList
        if (messagesList) {
          messagesList.scrollTop = messagesList.scrollHeight
        }
      })
    }
  },
  
  beforeUnmount() {
    // 组件销毁时关闭连接
    if (this.currentEventSource) {
      this.currentEventSource.close()
    }
    
    // 清除计时器
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer)
      this.recordingTimer = null
    }
    
    // 停止录音
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop()
      this.isRecording = false
    }
  }
}
</script>

<style scoped>
.chat-window {
  width: 100%;
  max-width: 984px;
  margin: 0 auto;
}

.chat-container {
  position: relative;
  background: #e5fbff;
  border: 1px solid rgba(114, 114, 114, 0.3);
  border-radius: 22px;
  padding: 24px;
  min-height: 400px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chat-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.bg-image-left {
  position: absolute;
  bottom: 24px;
  left: 24px;
  width: 18px;
  height: 17px;
  opacity: 0.6;
}

.bg-image-right {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 182px;
  height: 222px;
  opacity: 0.8;
}

.media-buttons {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.media-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.media-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.05);
}

.media-btn.active {
  background: #ff4444;
  color: white;
  animation: pulse 1s infinite;
}

.plus-btn {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
}

.history-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
}

.voice-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
}

.voice-btn.active {
  background: #ff4444;
  color: white;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.input-hint {
  position: absolute;
  top: 50%;
  left: 50px;
  transform: translateY(-50%);
  z-index: 5;
  pointer-events: none;
  max-width: 300px;
}

.input-hint p {
  font-size: 17px;
  color: #a4a4a4;
  margin: 0;
  font-family: 'Inter', sans-serif;
  line-height: 1.4;
}

.chat-input-container {
  position: absolute;
  bottom: 24px;
  left: 21px;
  right: 21px;
  z-index: 10;
  margin-top: auto;
}

.input-wrapper {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.36);
  border-radius: 12px;
  padding: 16px 20px;
  display: flex;
  align-items: flex-end;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 60px;
}

.image-preview {
  position: relative;
  flex-shrink: 0;
}

.image-preview img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-wrapper textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  font-family: inherit;
  min-height: 20px;
  max-height: 120px;
}

.input-wrapper textarea::placeholder {
  color: #999;
}

.send-btn {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  background: #e0e0e0;
  border: none;
  border-radius: 8px;
  color: #999;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn.can-send {
  background: #007bff;
  color: white;
}

.send-btn:hover.can-send {
  background: #0056b3;
}

.send-btn.loading {
  background: #6c757d;
  cursor: not-allowed;
  animation: pulse 1s infinite;
}

.recording-status {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.recording-status.status-connecting {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.recording-status.status-recording {
  background: rgba(255, 68, 68, 0.1);
  color: #ff4444;
}

.recording-status.status-transcribing {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.recording-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.recording-status.status-connecting .pulse {
  background: #ffc107;
}

.recording-status.status-recording .pulse {
  background: #ff4444;
}

.recording-status.status-transcribing .pulse {
  background: #007bff;
}

.stop-recording {
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.recording-status.status-connecting .stop-recording {
  background: #ffc107;
  color: #212529;
}

.recording-status.status-recording .stop-recording {
  background: #ff4444;
  color: white;
}

.recording-status.status-transcribing .stop-recording {
  background: #007bff;
  color: white;
}

.messages-list {
  position: absolute;
  top: 24px;
  left: 21px;
  right: 21px;
  bottom: 100px;
  overflow-y: auto;
  z-index: 5;
  padding-right: 8px;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
}

.ai-avatar,
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.ai-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-avatar {
  background: #007bff;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-bubble {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-message .message-bubble {
  background: #007bff;
  color: white;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}

.audio-message {
  display: flex;
  align-items: center;
  gap: 8px;
}

.play-audio-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 流式响应指示器 */
.streaming-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #007bff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 滚动条样式 */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    min-height: 300px;
    padding: 16px;
  }
  
  .media-buttons {
    top: 16px;
    right: 16px;
  }
  
  .media-btn {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .input-hint {
    left: 24px;
  }
  
  .input-hint p {
    font-size: 14px;
  }
  
  .chat-input-container {
    bottom: 16px;
    left: 16px;
    right: 16px;
  }
  
  .messages-list {
    top: 16px;
    left: 16px;
    right: 16px;
    bottom: 80px;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>
