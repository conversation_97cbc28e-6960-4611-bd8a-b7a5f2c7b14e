#!/bin/bash

# Faster-Whisper 语音识别服务启动脚本

echo "🚀 启动 Faster-Whisper 语音识别服务..."

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装"
    exit 1
fi

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 使用虚拟环境: $VIRTUAL_ENV"
else
    echo "⚠️  建议使用虚拟环境"
fi

# 安装依赖（如果需要）
if [ ! -f ".deps_installed" ]; then
    echo "📦 安装依赖包..."
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        touch .deps_installed
        echo "✅ 依赖安装完成"
    else
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 创建配置文件（如果不存在）
if [ ! -f ".env" ]; then
    echo "📝 创建配置文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 配置文件，请根据需要修改"
fi

# 启动服务
echo "🌐 启动 Web 服务..."
python3 start.py "$@"
