#!/usr/bin/env python3
"""
Faster-Whisper 语音识别服务启动脚本
"""

import os
import sys
import argparse
import logging
import uvicorn
from config import Config

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('whisper_service.log', encoding='utf-8')
        ]
    )

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'faster_whisper',
        'fastapi', 
        'uvicorn',
        'torch'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少必要的依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def create_directories():
    """创建必要的目录"""
    dirs_to_create = [
        Config.MODEL_DOWNLOAD_DIR,
        "logs",
        "uploads"
    ]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"📁 创建目录: {dir_path}")

def main():
    parser = argparse.ArgumentParser(description="Faster-Whisper 语音识别服务")
    parser.add_argument("--host", default=Config.HOST, help="服务器地址")
    parser.add_argument("--port", type=int, default=Config.PORT, help="端口号")
    parser.add_argument("--model", default=Config.WHISPER_MODEL_SIZE, help="模型大小")
    parser.add_argument("--device", default=Config.WHISPER_DEVICE, help="设备类型")
    parser.add_argument("--log-level", default=Config.LOG_LEVEL, help="日志级别")
    parser.add_argument("--reload", action="store_true", help="开发模式（自动重载）")
    parser.add_argument("--check-only", action="store_true", help="仅检查环境不启动服务")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    print("🚀 Faster-Whisper 语音识别服务")
    print("=" * 50)
    
    # 更新配置
    Config.HOST = args.host
    Config.PORT = args.port
    Config.WHISPER_MODEL_SIZE = args.model
    Config.WHISPER_DEVICE = args.device
    Config.LOG_LEVEL = args.log_level
    
    # 打印配置信息
    Config.print_config()
    
    # 验证配置
    if not Config.validate_config():
        logger.error("配置验证失败，请检查配置参数")
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    if args.check_only:
        print("✅ 环境检查完成，服务可以正常启动")
        return
    
    # 设置环境变量
    os.environ["WHISPER_MODEL_SIZE"] = args.model
    os.environ["WHISPER_DEVICE"] = args.device
    os.environ["LOG_LEVEL"] = args.log_level
    
    try:
        print(f"🌐 启动服务: http://{args.host}:{args.port}")
        print("📖 API 文档: http://{}:{}/docs".format(args.host, args.port))
        print("🛑 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        # 启动服务
        uvicorn.run(
            "app:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=1,  # Whisper 模型不适合多进程
            log_level=args.log_level.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
