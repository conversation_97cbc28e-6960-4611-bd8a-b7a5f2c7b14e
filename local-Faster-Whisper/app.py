import os
import logging
from typing import Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Body
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from whisper_service import WhisperTranscriptionService
from streaming_service import StreamingWhisperService, ChunkedUploadService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 FastAPI 应用
app = FastAPI(
    title="Faster-Whisper 语音识别服务",
    description="基于 Faster-Whisper 的本地语音识别转文字服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量存储服务实例
whisper_service: Optional[WhisperTranscriptionService] = None
streaming_service: Optional[StreamingWhisperService] = None
chunked_upload_service: Optional[ChunkedUploadService] = None


class TranscriptionRequest(BaseModel):
    """转录请求模型"""
    language: Optional[str] = None
    task: str = "transcribe"
    beam_size: int = 5
    best_of: int = 5
    temperature: float = 0.0


class TranscriptionResponse(BaseModel):
    """转录响应模型"""
    success: bool
    message: str
    data: Optional[dict] = None


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化模型"""
    global whisper_service, streaming_service, chunked_upload_service
    try:
        # 从环境变量获取配置，如果没有则使用默认值
        model_size = os.getenv("WHISPER_MODEL_SIZE", "base")
        device = os.getenv("WHISPER_DEVICE", "auto")
        compute_type = os.getenv("WHISPER_COMPUTE_TYPE", "default")
        
        logger.info(f"正在初始化 Whisper 服务，模型: {model_size}, 设备: {device}")
        whisper_service = WhisperTranscriptionService(
            model_size=model_size,
            device=device,
            compute_type=compute_type
        )
        
        # 初始化流式服务
        streaming_service = StreamingWhisperService(whisper_service)
        chunked_upload_service = ChunkedUploadService(whisper_service)
        
        logger.info("Whisper 服务和流式服务初始化成功")
    except Exception as e:
        logger.error(f"服务初始化失败: {e}")
        raise e


@app.get("/", response_model=dict)
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "Faster-Whisper 语音识别服务",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "transcribe": "/transcribe - POST 上传音频文件进行转录",
            "streaming": {
                "start": "/stream/start - POST 开始流式转录会话",
                "chunk": "/stream/chunk - POST 发送音频块",
                "end": "/stream/end - POST 结束流式会话"
            },
            "chunked_upload": {
                "start": "/upload/start - POST 开始分块上传",
                "chunk": "/upload/chunk - POST 上传文件块",
                "complete": "/upload/complete - POST 完成上传并转录"
            },
            "health": "/health - GET 健康检查",
            "info": "/info - GET 获取模型信息",
            "languages": "/languages - GET 获取支持的语言列表"
        }
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    if whisper_service is None:
        raise HTTPException(status_code=503, detail="服务未初始化")
    
    return {
        "status": "healthy",
        "service": "whisper-transcription",
        "model_loaded": whisper_service.model is not None
    }


@app.get("/info")
async def get_model_info():
    """获取模型信息"""
    if whisper_service is None:
        raise HTTPException(status_code=503, detail="服务未初始化")
    
    try:
        info = whisper_service.get_model_info()
        return {
            "success": True,
            "data": info
        }
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取模型信息失败: {e}")


@app.get("/languages")
async def get_supported_languages():
    """获取支持的语言列表"""
    if whisper_service is None:
        raise HTTPException(status_code=503, detail="服务未初始化")
    
    try:
        languages = whisper_service.get_supported_languages()
        return {
            "success": True,
            "data": {
                "languages": languages,
                "note": "使用 'auto' 进行自动语言检测"
            }
        }
    except Exception as e:
        logger.error(f"获取语言列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取语言列表失败: {e}")


@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    file: UploadFile = File(..., description="音频文件"),
    language: Optional[str] = Form(None, description="语言代码，如 'zh', 'en' 或 'auto'"),
    task: str = Form("transcribe", description="任务类型: 'transcribe' 或 'translate'"),
    beam_size: int = Form(5, description="Beam search 大小"),
    best_of: int = Form(5, description="采样数量"),
    temperature: float = Form(0.0, description="温度参数")
):
    """
    音频转录接口
    
    支持的音频格式: wav, mp3, m4a, flac, ogg 等
    """
    if whisper_service is None:
        raise HTTPException(status_code=503, detail="服务未初始化")
    
    # 检查文件大小 (限制为 25MB)
    max_file_size = 25 * 1024 * 1024  # 25MB
    if file.size and file.size > max_file_size:
        raise HTTPException(status_code=413, detail="文件大小超过限制 (25MB)")
    
    # 检查文件类型
    allowed_types = {
        "audio/wav", "audio/wave", "audio/x-wav",
        "audio/mp3", "audio/mpeg",
        "audio/mp4", "audio/m4a",
        "audio/flac",
        "audio/ogg", "audio/vorbis",
        "video/mp4", "video/avi", "video/mov"
    }
    
    if file.content_type and file.content_type not in allowed_types:
        logger.warning(f"文件类型可能不支持: {file.content_type}")
    
    try:
        # 读取文件内容
        audio_bytes = await file.read()
        logger.info(f"开始转录文件: {file.filename}, 大小: {len(audio_bytes)} bytes")
        
        # 执行转录
        result = whisper_service.transcribe_from_bytes(
            audio_bytes=audio_bytes,
            filename=file.filename or "audio.wav",
            language=language if language != "auto" else None,
            task=task,
            beam_size=beam_size,
            best_of=best_of,
            temperature=temperature
        )
        
        logger.info(f"转录完成: {file.filename}")
        
        return TranscriptionResponse(
            success=True,
            message="转录成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"转录失败: {e}")
        raise HTTPException(status_code=500, detail=f"转录失败: {str(e)}")


# =========================== 流式转录接口 ===========================

@app.post("/stream/start")
async def start_streaming_session(
    language: Optional[str] = Form(None, description="语言代码"),
    task: str = Form("transcribe", description="任务类型")
):
    """开始流式转录会话"""
    if streaming_service is None:
        raise HTTPException(status_code=503, detail="流式服务未初始化")
    
    try:
        session_id = streaming_service.create_session(language=language, task=task)
        
        return {
            "success": True,
            "message": "流式会话创建成功",
            "data": {
                "session_id": session_id,
                "language": language,
                "task": task
            }
        }
    except Exception as e:
        logger.error(f"创建流式会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@app.post("/stream/chunk")
async def send_audio_chunk(
    session_id: str = Form(..., description="会话ID"),
    audio_chunk: UploadFile = File(..., description="音频块"),
    process_now: bool = Form(False, description="是否立即处理")
):
    """发送音频块"""
    if streaming_service is None:
        raise HTTPException(status_code=503, detail="流式服务未初始化")
    
    try:
        # 读取音频块数据
        chunk_data = await audio_chunk.read()
        
        # 添加到会话
        success = streaming_service.add_audio_chunk(session_id, chunk_data)
        if not success:
            raise HTTPException(status_code=404, detail="会话不存在或已失效")
        
        result = {
            "success": True,
            "message": "音频块接收成功",
            "data": {
                "session_id": session_id,
                "chunk_size": len(chunk_data),
                "processed": False
            }
        }
        
        # 如果需要立即处理
        if process_now:
            transcription_result = streaming_service.process_chunk_transcription(session_id)
            result["data"]["transcription"] = transcription_result
            result["data"]["processed"] = True
        
        return result
        
    except Exception as e:
        logger.error(f"处理音频块失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理音频块失败: {str(e)}")


@app.post("/stream/process")
async def process_streaming_transcription(
    session_id: str = Form(..., description="会话ID")
):
    """处理当前会话的转录"""
    if streaming_service is None:
        raise HTTPException(status_code=503, detail="流式服务未初始化")
    
    try:
        result = streaming_service.process_chunk_transcription(session_id)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "转录处理成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"处理转录失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理转录失败: {str(e)}")


@app.post("/stream/end")
async def end_streaming_session(
    session_id: str = Form(..., description="会话ID")
):
    """结束流式转录会话"""
    if streaming_service is None:
        raise HTTPException(status_code=503, detail="流式服务未初始化")
    
    try:
        result = streaming_service.end_session(session_id)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "会话结束成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"结束会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"结束会话失败: {str(e)}")


# =========================== 分块上传接口 ===========================

@app.post("/upload/start")
async def start_chunked_upload(
    filename: str = Form(..., description="文件名"),
    total_size: int = Form(..., description="文件总大小"),
    chunk_size: int = Form(1048576, description="块大小")
):
    """开始分块上传"""
    if chunked_upload_service is None:
        raise HTTPException(status_code=503, detail="分块上传服务未初始化")
    
    try:
        upload_id = chunked_upload_service.start_chunked_upload(filename, total_size, chunk_size)
        
        return {
            "success": True,
            "message": "分块上传会话创建成功",
            "data": {
                "upload_id": upload_id,
                "filename": filename,
                "total_size": total_size,
                "chunk_size": chunk_size
            }
        }
        
    except Exception as e:
        logger.error(f"创建分块上传会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建上传会话失败: {str(e)}")


@app.post("/upload/chunk")
async def upload_file_chunk(
    upload_id: str = Form(..., description="上传ID"),
    chunk_number: int = Form(..., description="块编号"),
    chunk_data: UploadFile = File(..., description="文件块")
):
    """上传文件块"""
    if chunked_upload_service is None:
        raise HTTPException(status_code=503, detail="分块上传服务未初始化")
    
    try:
        # 读取块数据
        data = await chunk_data.read()
        
        # 上传块
        result = chunked_upload_service.upload_chunk(upload_id, chunk_number, data)
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "文件块上传成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"上传文件块失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传文件块失败: {str(e)}")


@app.post("/upload/complete")
async def complete_chunked_upload(
    upload_id: str = Form(..., description="上传ID"),
    language: Optional[str] = Form(None, description="语言代码"),
    task: str = Form("transcribe", description="任务类型"),
    beam_size: int = Form(5, description="Beam search 大小"),
    best_of: int = Form(5, description="采样数量"),
    temperature: float = Form(0.0, description="温度参数")
):
    """完成分块上传并进行转录"""
    if chunked_upload_service is None:
        raise HTTPException(status_code=503, detail="分块上传服务未初始化")
    
    try:
        # 完成上传并转录
        result = chunked_upload_service.complete_chunked_upload(
            upload_id,
            language=language,
            task=task,
            beam_size=beam_size,
            best_of=best_of,
            temperature=temperature
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "message": "文件上传完成，转录成功",
            "data": result
        }
        
    except Exception as e:
        logger.error(f"完成上传和转录失败: {e}")
        raise HTTPException(status_code=500, detail=f"完成上传失败: {str(e)}")


@app.get("/upload/status/{upload_id}")
async def get_upload_status(upload_id: str):
    """获取上传状态"""
    if chunked_upload_service is None:
        raise HTTPException(status_code=503, detail="分块上传服务未初始化")
    
    try:
        result = chunked_upload_service.get_upload_status(upload_id)
        
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        
        return {
            "success": True,
            "data": result
        }
        
    except Exception as e:
        logger.error(f"获取上传状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "detail": str(exc)
        }
    )


if __name__ == "__main__":
    # 从环境变量获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    
    uvicorn.run(
        "app:app",
        host=host,
        port=port,
        reload=False,  # 生产环境设置为 False
        workers=1,     # Whisper 模型不适合多进程
        log_level="info"
    )
