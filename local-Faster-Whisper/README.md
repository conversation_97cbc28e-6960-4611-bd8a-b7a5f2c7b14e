# Faster-Whisper 本地语音识别服务

基于 [Faster-Whisper](https://github.com/guillaumekln/faster-whisper) 的高性能本地语音识别转文字服务，提供简单易用的 REST API 接口。

## ✨ 特性

- 🚀 **高性能**: 基于 Faster-Whisper，比原版 Whisper 快 4 倍，内存使用更少
- 🌍 **多语言支持**: 支持中文、英文、日文等 90+ 种语言
- 🔧 **易于部署**: 简单的安装和配置过程
- 📝 **完整 API**: 提供 RESTful API 和交互式文档
- ⚙️ **灵活配置**: 支持多种模型大小和设备类型
- 📊 **详细输出**: 提供词级别时间戳和置信度

## 🛠️ 系统要求

- Python 3.8+
- 4GB+ RAM (推荐 8GB+)
- 可选: NVIDIA GPU (支持 CUDA)

## 📦 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd local-Faster-Whisper
```

### 2. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\\Scripts\\activate   # Windows
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置服务

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 5. 启动服务

```bash
# 方式 1: 使用 Python 脚本
python start.py

# 方式 2: 使用 Shell 脚本 (Linux/Mac)
chmod +x run.sh
./run.sh

# 方式 3: 直接启动
python -m uvicorn app:app --host 0.0.0.0 --port 8000
```

## 📋 配置说明

### 环境变量配置 (.env)

```bash
# 服务器配置
HOST=0.0.0.0              # 服务器地址
PORT=8000                 # 端口号

# 模型配置
WHISPER_MODEL_SIZE=base   # 模型大小: tiny, base, small, medium, large-v3
WHISPER_DEVICE=auto       # 设备: auto, cpu, cuda
WHISPER_COMPUTE_TYPE=default  # 计算类型: default, int8, float16

# 其他配置
LOG_LEVEL=INFO           # 日志级别
MAX_FILE_SIZE=26214400   # 最大文件大小 (25MB)
```

### 模型大小说明

| 模型 | 参数量 | 内存使用 | 速度 | 准确性 |
|-----|-------|---------|------|-------|
| tiny | 39M | ~1GB | 最快 | 一般 |
| base | 74M | ~1GB | 快 | 良好 |
| small | 244M | ~2GB | 中等 | 很好 |
| medium | 769M | ~5GB | 慢 | 优秀 |
| large-v3 | 1550M | ~10GB | 最慢 | 最佳 |

## 🌐 API 使用

服务启动后，可以通过以下方式使用：

### API 文档

- 交互式文档: http://localhost:8000/docs
- OpenAPI JSON: http://localhost:8000/openapi.json

### 主要接口

#### 1. 健康检查

```bash
curl http://localhost:8000/health
```

#### 2. 获取模型信息

```bash
curl http://localhost:8000/info
```

#### 3. 语音转录

```bash
curl -X POST http://localhost:8000/transcribe \\
  -F "file=@your_audio.wav" \\
  -F "language=zh" \\
  -F "task=transcribe"
```

#### 4. 使用 Python 调用

```python
import requests

# 上传音频文件进行转录
url = "http://localhost:8000/transcribe"
files = {"file": open("audio.wav", "rb")}
data = {
    "language": "zh",  # 可选：语言代码
    "task": "transcribe"  # transcribe 或 translate
}

response = requests.post(url, files=files, data=data)
result = response.json()

if result["success"]:
    print("转录文本:", result["data"]["text"])
    print("检测语言:", result["data"]["language"])
    
    # 获取详细片段信息
    for segment in result["data"]["segments"]:
        print(f"[{segment['start']:.2f}s - {segment['end']:.2f}s]: {segment['text']}")
```

### 响应格式

```json
{
  "success": true,
  "message": "转录成功",
  "data": {
    "language": "zh",
    "language_probability": 0.99,
    "duration": 10.5,
    "text": "完整的转录文本",
    "segments": [
      {
        "start": 0.0,
        "end": 3.5,
        "text": "这是第一段文本",
        "words": [
          {
            "start": 0.0,
            "end": 0.5,
            "word": "这是",
            "probability": 0.98
          }
        ]
      }
    ]
  }
}
```

## 🎯 高级用法

### 自定义启动参数

```bash
python start.py --help

# 自定义配置启动
python start.py \\
  --host 0.0.0.0 \\
  --port 8080 \\
  --model small \\
  --device cuda \\
  --log-level DEBUG
```

### 批量处理脚本

```python
import os
import requests
from pathlib import Path

def transcribe_directory(audio_dir, output_dir, server_url="http://localhost:8000"):
    """批量转录目录中的音频文件"""
    audio_dir = Path(audio_dir)
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for audio_file in audio_dir.glob("*.wav"):
        print(f"正在处理: {audio_file.name}")
        
        with open(audio_file, "rb") as f:
            files = {"file": f}
            response = requests.post(f"{server_url}/transcribe", files=files)
            
        if response.status_code == 200:
            result = response.json()
            if result["success"]:
                # 保存转录结果
                output_file = output_dir / f"{audio_file.stem}.txt"
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(result["data"]["text"])
                print(f"✅ 转录完成: {output_file}")
            else:
                print(f"❌ 转录失败: {result['message']}")
        else:
            print(f"❌ 请求失败: {response.status_code}")

# 使用示例
# transcribe_directory("./audio_files", "./transcripts")
```

## 🔧 故障排除

### 常见问题

1. **模型下载慢**
   - 使用较小的模型 (tiny, base)
   - 检查网络连接
   - 考虑使用代理

2. **内存不足**
   - 使用较小的模型
   - 设置 `compute_type=int8`
   - 减少 `beam_size` 参数

3. **CUDA 错误**
   - 检查 CUDA 和 PyTorch 版本兼容性
   - 设置 `device=cpu` 使用 CPU

4. **转录质量差**
   - 使用更大的模型
   - 确保音频质量良好
   - 指定正确的语言代码

### 性能优化

1. **GPU 加速**
   ```bash
   # 安装 CUDA 版本的 PyTorch
   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
   
   # 设置使用 GPU
   export WHISPER_DEVICE=cuda
   ```

2. **内存优化**
   ```bash
   # 使用 int8 量化
   export WHISPER_COMPUTE_TYPE=int8
   ```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：

- GitHub Issues: 提交技术问题
- Email: 发送邮件咨询

---

**注意**: 首次运行时，系统会自动下载所选的 Whisper 模型，这可能需要一些时间。模型文件会保存在本地，后续启动会更快。
 source /Users/<USER>/Desktop/local-Faster-Whisper/.venv/bin/activate
 python start.py --model=tiny
 python test_client.py --url http://localhost:8008 --file test.m4a