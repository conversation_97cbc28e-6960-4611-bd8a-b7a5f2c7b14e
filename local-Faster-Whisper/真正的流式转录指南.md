# 🎙️ 真正的实时流式音频转录

## 🆚 流式 vs 传统处理对比

### ❌ 传统方式 (之前的实现)
```
用户说话 → 录制音频块 → 上传文件 → 服务端处理 → 返回结果
延迟: 2-5秒  |  需要等待完整音频块  |  不是真正的实时
```

### ✅ 真正的流式处理 (新实现)
```
用户说话 → 实时音频流 → WebSocket传输 → 即时处理 → 实时反馈
延迟: <500ms  |  持续音频流  |  真正的实时体验
```

## 🏗️ 新架构设计

### 1. WebSocket 实时通信
- **协议**: WebSocket (ws://localhost:8765)
- **数据格式**: JSON + Base64音频
- **连接**: 持久连接，支持双向通信
- **心跳**: 自动心跳检测和重连

### 2. 音频流处理
```javascript
麦克风 → AudioContext → ScriptProcessor → 音频缓冲区 → WebSocket
 ↓
16kHz, 单声道, Float32 → Base64编码 → 实时传输
```

### 3. 服务端流式处理
```python
WebSocket接收 → 音频解码 → 缓冲区合并 → Whisper转录 → 实时返回
 ↓
多线程处理 + 异步IO + 会话管理
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install websockets>=12.0
```

### 2. 启动完整服务
```bash
# 方式1: 启动完整服务 (HTTP + WebSocket)
python start_streaming.py

# 方式2: 仅启动WebSocket服务
python real_time_streaming.py

# 方式3: 分别启动
python start.py --port 8008  # HTTP服务
python real_time_streaming.py  # WebSocket服务
```

### 3. 测试服务
```bash
# 检查HTTP服务
curl http://localhost:8008/health

# 检查WebSocket服务 (使用浏览器开发者工具)
const ws = new WebSocket('ws://localhost:8765');
ws.onopen = () => console.log('连接成功');
```

## 💻 客户端使用

### JavaScript 客户端

#### 基础使用
```javascript
// 创建实时转录客户端
const client = new RealTimeWhisperClient({
    wsUrl: 'ws://localhost:8765',
    language: 'zh',
    onTranscription: (result) => {
        console.log('转录结果:', result.text);
        document.getElementById('output').textContent = result.text;
    },
    onError: (error) => {
        console.error('错误:', error);
    }
});

// 开始录音
await client.startRecording();

// 停止录音
await client.stopRecording();
```

#### 高级管理器
```javascript
// 使用管理器进行更高级的控制
const manager = new RealtimeTranscriptionManager({
    wsUrl: 'ws://localhost:8765',
    language: 'zh',
    maxReconnectAttempts: 5,
    onTranscription: (result) => {
        // 实时显示转录结果
        updateTranscriptionDisplay(result);
    },
    onStatusChange: (status) => {
        // 状态变化：connected, recording, disconnected, stopped
        updateStatusIndicator(status);
    }
});

// 开始实时转录
await manager.start();

// 获取转录历史
const history = manager.getTranscriptionHistory();
console.log('转录历史:', history);

// 停止转录
await manager.stop();
```

### 网页端完整示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>实时语音转录</title>
</head>
<body>
    <div id="app">
        <button id="startBtn">开始录音</button>
        <button id="stopBtn" disabled>停止录音</button>
        <div id="status">准备就绪</div>
        <div id="output">等待转录结果...</div>
    </div>

    <script src="websocket_client.js"></script>
    <script>
        let manager = null;

        document.getElementById('startBtn').onclick = async () => {
            try {
                manager = new RealtimeTranscriptionManager({
                    wsUrl: 'ws://localhost:8765',
                    language: 'zh',
                    onTranscription: (result) => {
                        document.getElementById('output').textContent = result.text;
                    },
                    onStatusChange: (status) => {
                        document.getElementById('status').textContent = `状态: ${status}`;
                    }
                });

                await manager.start();
                
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
            } catch (error) {
                alert('启动失败: ' + error.message);
            }
        };

        document.getElementById('stopBtn').onclick = async () => {
            if (manager) {
                await manager.stop();
                manager = null;
                
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }
        };
    </script>
</body>
</html>
```

## 📡 WebSocket 协议

### 连接建立
```javascript
// 客户端连接
const ws = new WebSocket('ws://localhost:8765');

// 服务端响应
{
  "type": "connected",
  "timestamp": "2024-08-24T20:00:00",
  "message": "连接成功",
  "config": {
    "sample_rate": 16000,
    "chunk_duration": 2.0,
    "min_audio_length": 1.0
  }
}
```

### 配置语言
```javascript
// 发送配置
ws.send(JSON.stringify({
  "type": "config",
  "language": "zh",
  "task": "transcribe"
}));

// 服务端确认
{
  "type": "config_updated",
  "timestamp": "2024-08-24T20:00:01",
  "config": {
    "language": "zh",
    "task": "transcribe"
  }
}
```

### 发送音频数据
```javascript
// 音频数据格式
ws.send(JSON.stringify({
  "type": "audio_data",
  "audio_data": "base64_encoded_audio_data",
  "sample_rate": 16000,
  "samples": 32000
}));

// 服务端确认
{
  "type": "audio_received",
  "timestamp": "2024-08-24T20:00:02",
  "buffer_size": 3,
  "total_bytes": 128000
}
```

### 接收转录结果
```javascript
// 转录结果
{
  "type": "transcription",
  "timestamp": "2024-08-24T20:00:03",
  "data": {
    "language": "zh",
    "language_probability": 0.99,
    "duration": 2.5,
    "text": "你好，这是实时转录测试",
    "segments": [
      {
        "start": 0.0,
        "end": 2.5,
        "text": "你好，这是实时转录测试"
      }
    ]
  }
}
```

### 错误处理
```javascript
// 错误消息
{
  "type": "error",
  "timestamp": "2024-08-24T20:00:04",
  "message": "音频处理失败: 格式不支持"
}
```

### 心跳检测
```javascript
// 客户端心跳
ws.send(JSON.stringify({ "type": "ping" }));

// 服务端响应
{
  "type": "pong",
  "timestamp": "2024-08-24T20:00:05"
}
```

## ⚡ 性能优化

### 1. 音频参数优化
```javascript
const optimizedConfig = {
    sampleRate: 16000,     // Whisper标准采样率
    bufferSize: 4096,      // 较小的缓冲区，降低延迟
    sendInterval: 500,     // 500ms发送间隔
    channels: 1            // 单声道减少数据量
};
```

### 2. 网络优化
```javascript
// 音频压缩和批量发送
class AudioOptimizer {
    constructor() {
        this.buffer = [];
        this.lastSendTime = 0;
        this.minInterval = 300; // 最小发送间隔
    }
    
    addAudio(audioData) {
        this.buffer.push(...audioData);
        
        const now = Date.now();
        if (now - this.lastSendTime >= this.minInterval) {
            this.sendBuffer();
            this.lastSendTime = now;
        }
    }
    
    sendBuffer() {
        if (this.buffer.length > 0) {
            // 发送合并的音频数据
            this.sendAudioData(this.buffer);
            this.buffer = [];
        }
    }
}
```

### 3. 服务端优化
```python
# 音频处理线程池
import concurrent.futures

class OptimizedStreamingService:
    def __init__(self):
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
    
    def process_audio_async(self, audio_data):
        # 异步处理音频，不阻塞WebSocket
        future = self.executor.submit(self.transcribe_audio, audio_data)
        return future
```

## 🔧 配置选项

### 客户端配置
```javascript
const config = {
    // WebSocket配置
    wsUrl: 'ws://localhost:8765',
    reconnectDelay: 2000,
    maxReconnectAttempts: 5,
    pingInterval: 30000,
    
    // 音频配置
    sampleRate: 16000,
    bufferSize: 4096,
    sendInterval: 500,
    
    // 转录配置
    language: 'zh',
    task: 'transcribe',
    
    // 回调函数
    onTranscription: (result) => {},
    onError: (error) => {},
    onStatusChange: (status) => {}
};
```

### 服务端配置
```python
# 环境变量配置
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8765
CHUNK_DURATION=2.0
MIN_AUDIO_LENGTH=1.0
MAX_CONNECTIONS=50
PING_INTERVAL=30
PING_TIMEOUT=10
```

## 🚨 注意事项

### 1. 浏览器兼容性
- **需要HTTPS**: 生产环境必须使用HTTPS和WSS
- **麦克风权限**: 需要用户明确授权
- **音频格式**: 浏览器支持的音频格式可能不同

### 2. 网络要求
- **稳定连接**: WebSocket需要稳定的网络连接
- **带宽**: 音频流需要足够的上传带宽
- **延迟**: 网络延迟会影响实时性

### 3. 服务端资源
- **内存使用**: 多个并发连接会消耗较多内存
- **CPU负载**: 实时转录对CPU要求较高
- **并发限制**: 建议限制同时连接数

### 4. 错误恢复
```javascript
// 自动重连机制
class RobustWebSocketClient {
    constructor(url) {
        this.url = url;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(this.url);
        
        this.ws.onclose = () => {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                setTimeout(() => this.connect(), 2000 * this.reconnectAttempts);
            }
        };
        
        this.ws.onopen = () => {
            this.reconnectAttempts = 0; // 重置重连计数
        };
    }
}
```

## 📊 监控和调试

### 1. 性能监控
```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            audioLatency: [],
            transcriptionLatency: [],
            connectionDrops: 0,
            totalTranscriptions: 0
        };
    }
    
    recordAudioLatency(latency) {
        this.metrics.audioLatency.push(latency);
    }
    
    recordTranscriptionLatency(latency) {
        this.metrics.transcriptionLatency.push(latency);
    }
    
    getAverageLatency() {
        const avg = arr => arr.reduce((a, b) => a + b, 0) / arr.length;
        return {
            audio: avg(this.metrics.audioLatency),
            transcription: avg(this.metrics.transcriptionLatency)
        };
    }
}
```

### 2. 调试工具
```javascript
// 开启调试模式
const debugClient = new RealTimeWhisperClient({
    wsUrl: 'ws://localhost:8765',
    debug: true,
    onDebug: (event, data) => {
        console.log(`[DEBUG] ${event}:`, data);
    }
});
```

## 🎯 使用场景

### 1. 实时会议转录
- 会议记录
- 多语言翻译
- 字幕生成

### 2. 语音助手
- 语音命令识别
- 智能客服
- 语音控制

### 3. 直播字幕
- 实时直播字幕
- 多平台同步
- 自动断句

### 4. 教育应用
- 在线课程转录
- 语言学习
- 听力辅助

---

通过这个真正的流式架构，你现在可以实现：
- ⚡ **低延迟**: <500ms的实时反馈
- 🔄 **真正流式**: 持续的音频流处理
- 🛡️ **高可用**: 自动重连和错误恢复
- 📱 **跨平台**: Web、移动端、桌面应用
- 🎯 **生产就绪**: 完整的监控和调试工具
