#!/usr/bin/env python3
"""
Faster-Whisper 流式和分块上传客户端
支持实时音频流转录和大文件分块上传
"""

import requests
import asyncio
import aiohttp
import json
import time
import os
from typing import Optional, Dict, Any, Callable
from pathlib import Path
import math


class StreamingWhisperClient:
    """流式 Whisper 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def create_session(self, language: Optional[str] = None, task: str = "transcribe") -> str:
        """创建流式转录会话"""
        data = {"task": task}
        if language:
            data["language"] = language
        
        response = self.session.post(f"{self.base_url}/stream/start", data=data)
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            session_id = result["data"]["session_id"]
            print(f"✅ 创建流式会话成功: {session_id}")
            return session_id
        else:
            raise Exception(f"创建会话失败: {result['message']}")
    
    def send_audio_chunk(self, session_id: str, audio_data: bytes, process_now: bool = False) -> dict:
        """发送音频块"""
        files = {"audio_chunk": ("chunk.wav", audio_data, "audio/wav")}
        data = {
            "session_id": session_id,
            "process_now": process_now
        }
        
        response = self.session.post(f"{self.base_url}/stream/chunk", files=files, data=data)
        response.raise_for_status()
        
        return response.json()
    
    def process_transcription(self, session_id: str) -> dict:
        """处理当前会话的转录"""
        data = {"session_id": session_id}
        
        response = self.session.post(f"{self.base_url}/stream/process", data=data)
        response.raise_for_status()
        
        return response.json()
    
    def end_session(self, session_id: str) -> dict:
        """结束流式会话"""
        data = {"session_id": session_id}
        
        response = self.session.post(f"{self.base_url}/stream/end", data=data)
        response.raise_for_status()
        
        return response.json()
    
    def transcribe_audio_stream(self, 
                               audio_chunks: list, 
                               language: Optional[str] = None,
                               chunk_process_interval: int = 3) -> dict:
        """转录音频流"""
        session_id = self.create_session(language=language)
        
        try:
            print(f"🎵 开始处理 {len(audio_chunks)} 个音频块")
            
            for i, chunk in enumerate(audio_chunks):
                # 发送音频块
                process_now = (i + 1) % chunk_process_interval == 0  # 每3个块处理一次
                
                result = self.send_audio_chunk(session_id, chunk, process_now)
                print(f"📦 发送块 {i+1}/{len(audio_chunks)}: {result['data']['chunk_size']} bytes")
                
                # 如果立即处理了，显示结果
                if process_now and result["data"]["processed"]:
                    transcription = result["data"]["transcription"]
                    if "transcription" in transcription:
                        text = transcription["transcription"].get("text", "")
                        print(f"🔤 中间结果: {text[:100]}...")
            
            # 结束会话并获取最终结果
            final_result = self.end_session(session_id)
            print("✅ 流式转录完成")
            
            return final_result
            
        except Exception as e:
            print(f"❌ 流式转录失败: {e}")
            # 尝试清理会话
            try:
                self.end_session(session_id)
            except:
                pass
            raise e


class ChunkedUploadClient:
    """分块上传客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def upload_large_file(self, 
                         file_path: str,
                         chunk_size: int = 1024 * 1024,  # 1MB
                         language: Optional[str] = None,
                         task: str = "transcribe",
                         progress_callback: Optional[Callable] = None) -> dict:
        """上传大文件并转录"""
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_size = file_path.stat().st_size
        total_chunks = math.ceil(file_size / chunk_size)
        
        print(f"📁 准备上传文件: {file_path.name}")
        print(f"   文件大小: {file_size / (1024*1024):.2f}MB")
        print(f"   块大小: {chunk_size / (1024*1024):.2f}MB")
        print(f"   总块数: {total_chunks}")
        
        # 1. 开始分块上传
        upload_id = self._start_upload(file_path.name, file_size, chunk_size)
        
        try:
            # 2. 上传所有块
            with open(file_path, 'rb') as f:
                for chunk_num in range(total_chunks):
                    chunk_data = f.read(chunk_size)
                    if not chunk_data:
                        break
                    
                    result = self._upload_chunk(upload_id, chunk_num, chunk_data)
                    
                    progress = result["data"]["progress"]
                    print(f"📦 上传块 {chunk_num + 1}/{total_chunks} ({progress:.1f}%)")
                    
                    if progress_callback:
                        progress_callback(chunk_num + 1, total_chunks, progress)
            
            # 3. 完成上传并转录
            print("🔄 开始转录...")
            final_result = self._complete_upload(upload_id, language, task)
            
            print("✅ 分块上传和转录完成")
            return final_result
            
        except Exception as e:
            print(f"❌ 分块上传失败: {e}")
            raise e
    
    def _start_upload(self, filename: str, total_size: int, chunk_size: int) -> str:
        """开始分块上传"""
        data = {
            "filename": filename,
            "total_size": total_size,
            "chunk_size": chunk_size
        }
        
        response = self.session.post(f"{self.base_url}/upload/start", data=data)
        response.raise_for_status()
        
        result = response.json()
        if result["success"]:
            upload_id = result["data"]["upload_id"]
            print(f"✅ 创建分块上传会话: {upload_id}")
            return upload_id
        else:
            raise Exception(f"创建上传会话失败: {result['message']}")
    
    def _upload_chunk(self, upload_id: str, chunk_number: int, chunk_data: bytes) -> dict:
        """上传单个块"""
        files = {"chunk_data": ("chunk", chunk_data, "application/octet-stream")}
        data = {
            "upload_id": upload_id,
            "chunk_number": chunk_number
        }
        
        response = self.session.post(f"{self.base_url}/upload/chunk", files=files, data=data)
        response.raise_for_status()
        
        return response.json()
    
    def _complete_upload(self, upload_id: str, language: Optional[str], task: str) -> dict:
        """完成上传并转录"""
        data = {
            "upload_id": upload_id,
            "task": task
        }
        if language:
            data["language"] = language
        
        response = self.session.post(f"{self.base_url}/upload/complete", data=data, timeout=600)
        response.raise_for_status()
        
        return response.json()
    
    def get_upload_status(self, upload_id: str) -> dict:
        """获取上传状态"""
        response = self.session.get(f"{self.base_url}/upload/status/{upload_id}")
        response.raise_for_status()
        
        return response.json()


class AsyncStreamingClient:
    """异步流式客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url.rstrip('/')
    
    async def transcribe_realtime_stream(self, 
                                       audio_stream_generator,
                                       language: Optional[str] = None,
                                       process_interval: float = 2.0) -> dict:
        """实时音频流转录"""
        async with aiohttp.ClientSession() as session:
            # 创建会话
            session_id = await self._create_async_session(session, language)
            
            try:
                print(f"🎤 开始实时流式转录: {session_id}")
                
                chunk_count = 0
                async for audio_chunk in audio_stream_generator:
                    chunk_count += 1
                    
                    # 发送音频块
                    await self._send_async_chunk(session, session_id, audio_chunk)
                    print(f"📦 发送音频块 {chunk_count}")
                    
                    # 定期处理转录
                    if chunk_count % 3 == 0:  # 每3个块处理一次
                        result = await self._process_async_transcription(session, session_id)
                        if result and "data" in result:
                            transcription = result["data"].get("transcription", {})
                            if "text" in transcription:
                                print(f"🔤 实时结果: {transcription['text'][:100]}...")
                    
                    await asyncio.sleep(process_interval)
                
                # 获取最终结果
                final_result = await self._end_async_session(session, session_id)
                print("✅ 实时流式转录完成")
                
                return final_result
                
            except Exception as e:
                print(f"❌ 实时转录失败: {e}")
                try:
                    await self._end_async_session(session, session_id)
                except:
                    pass
                raise e
    
    async def _create_async_session(self, session: aiohttp.ClientSession, language: Optional[str]) -> str:
        """异步创建会话"""
        data = aiohttp.FormData()
        data.add_field('task', 'transcribe')
        if language:
            data.add_field('language', language)
        
        async with session.post(f"{self.base_url}/stream/start", data=data) as response:
            response.raise_for_status()
            result = await response.json()
            
            if result["success"]:
                return result["data"]["session_id"]
            else:
                raise Exception(f"创建会话失败: {result['message']}")
    
    async def _send_async_chunk(self, session: aiohttp.ClientSession, session_id: str, audio_data: bytes):
        """异步发送音频块"""
        data = aiohttp.FormData()
        data.add_field('session_id', session_id)
        data.add_field('process_now', 'false')
        data.add_field('audio_chunk', audio_data, filename='chunk.wav', content_type='audio/wav')
        
        async with session.post(f"{self.base_url}/stream/chunk", data=data) as response:
            response.raise_for_status()
            return await response.json()
    
    async def _process_async_transcription(self, session: aiohttp.ClientSession, session_id: str):
        """异步处理转录"""
        data = aiohttp.FormData()
        data.add_field('session_id', session_id)
        
        async with session.post(f"{self.base_url}/stream/process", data=data) as response:
            response.raise_for_status()
            return await response.json()
    
    async def _end_async_session(self, session: aiohttp.ClientSession, session_id: str):
        """异步结束会话"""
        data = aiohttp.FormData()
        data.add_field('session_id', session_id)
        
        async with session.post(f"{self.base_url}/stream/end", data=data) as response:
            response.raise_for_status()
            return await response.json()


def main():
    """示例用法"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Faster-Whisper 流式和分块上传客户端")
    parser.add_argument("--url", default="http://localhost:8008", help="服务地址")
    parser.add_argument("--mode", choices=["stream", "chunk"], default="chunk", help="模式")
    parser.add_argument("--file", help="音频文件路径")
    parser.add_argument("--language", help="语言代码")
    parser.add_argument("--chunk-size", type=int, default=1048576, help="块大小 (字节)")
    
    args = parser.parse_args()
    
    if not args.file:
        print("请指定音频文件路径")
        return
    
    if args.mode == "chunk":
        # 分块上传模式
        client = ChunkedUploadClient(args.url)
        
        def progress_callback(current, total, progress):
            print(f"上传进度: {current}/{total} ({progress:.1f}%)")
        
        try:
            result = client.upload_large_file(
                args.file, 
                chunk_size=args.chunk_size,
                language=args.language,
                progress_callback=progress_callback
            )
            
            if result["success"]:
                transcription = result["data"]["transcription"]
                print("\n📝 转录结果:")
                print(f"语言: {transcription['language']}")
                print(f"文本: {transcription['text']}")
            
        except Exception as e:
            print(f"❌ 分块上传失败: {e}")
    
    elif args.mode == "stream":
        # 流式模式（将文件分块处理）
        client = StreamingWhisperClient(args.url)
        
        # 读取文件并分块
        chunk_size = 1024 * 1024  # 1MB per chunk
        chunks = []
        
        with open(args.file, 'rb') as f:
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                chunks.append(chunk)
        
        try:
            result = client.transcribe_audio_stream(chunks, language=args.language)
            
            if result["success"]:
                transcription = result["data"]["final_transcription"]
                print("\n📝 流式转录结果:")
                print(f"语言: {transcription['language']}")
                print(f"文本: {transcription['text']}")
            
        except Exception as e:
            print(f"❌ 流式转录失败: {e}")


if __name__ == "__main__":
    main()
