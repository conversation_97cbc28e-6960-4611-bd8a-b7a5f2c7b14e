import os
import tempfile
from typing import Optional, Dict, Any
from faster_whisper import WhisperModel
import torch


class WhisperTranscriptionService:
    """Faster-Whisper 语音识别服务类"""
    
    def __init__(self, model_size: str = "base", device: str = "auto", compute_type: str = "default"):
        """
        初始化 Whisper 模型
        
        Args:
            model_size: 模型大小 (tiny, base, small, medium, large-v2, large-v3)
            device: 设备类型 (auto, cpu, cuda)
            compute_type: 计算类型 (default, int8, float16)
        """
        self.model_size = model_size
        self.device = self._get_device(device)
        self.compute_type = self._get_compute_type(compute_type)
        self.model = None
        self._load_model()
    
    def _get_device(self, device: str) -> str:
        """自动检测或设置设备"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "cpu"  # MPS暂时不支持，使用CPU
            else:
                return "cpu"
        return device
    
    def _get_compute_type(self, compute_type: str) -> str:
        """根据设备自动设置计算类型"""
        if compute_type == "default":
            if self.device == "cuda":
                return "float16"
            else:
                return "int8"
        return compute_type
    
    def _load_model(self):
        """加载 Whisper 模型"""
        try:
            print(f"正在加载模型: {self.model_size}, 设备: {self.device}, 计算类型: {self.compute_type}")
            self.model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=self.compute_type,
                download_root="./models"  # 模型下载到本地models目录
            )
            print("模型加载成功!")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise e
    
    def transcribe_audio(self, 
                        audio_path: str, 
                        language: Optional[str] = None,
                        task: str = "transcribe",
                        beam_size: int = 5,
                        best_of: int = 5,
                        temperature: float = 0.0) -> Dict[str, Any]:
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 语言代码 (如 "zh", "en", None为自动检测)
            task: 任务类型 ("transcribe" 或 "translate")
            beam_size: beam search 大小
            best_of: 采样数量
            temperature: 温度参数
            
        Returns:
            包含转录结果的字典
        """
        if not self.model:
            raise RuntimeError("模型未加载")
        
        try:
            # 执行转录
            segments, info = self.model.transcribe(
                audio_path,
                language=language,
                task=task,
                beam_size=beam_size,
                best_of=best_of,
                temperature=temperature,
                vad_filter=True,  # 启用语音活动检测
                vad_parameters=dict(min_silence_duration_ms=500)
            )
            
            # 收集所有片段
            transcription_segments = []
            full_text = ""
            
            for segment in segments:
                segment_dict = {
                    "start": round(segment.start, 2),
                    "end": round(segment.end, 2),
                    "text": segment.text.strip(),
                    "words": []
                }
                
                # 如果有词级别的时间戳
                if hasattr(segment, 'words') and segment.words:
                    for word in segment.words:
                        segment_dict["words"].append({
                            "start": round(word.start, 2),
                            "end": round(word.end, 2),
                            "word": word.word,
                            "probability": round(word.probability, 3)
                        })
                
                transcription_segments.append(segment_dict)
                full_text += segment.text
            
            result = {
                "language": info.language,
                "language_probability": round(info.language_probability, 3),
                "duration": round(info.duration, 2),
                "text": full_text.strip(),
                "segments": transcription_segments
            }
            
            return result
            
        except Exception as e:
            raise RuntimeError(f"转录失败: {e}")
    
    def transcribe_from_bytes(self, 
                             audio_bytes: bytes, 
                             filename: str = "audio.wav",
                             **kwargs) -> Dict[str, Any]:
        """
        从字节数据转录音频
        
        Args:
            audio_bytes: 音频字节数据
            filename: 临时文件名
            **kwargs: 其他转录参数
            
        Returns:
            转录结果字典
        """
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1]) as temp_file:
            temp_file.write(audio_bytes)
            temp_path = temp_file.name
        
        try:
            # 执行转录
            result = self.transcribe_audio(temp_path, **kwargs)
            return result
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
    
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        return [
            "auto", "zh", "en", "ja", "ko", "fr", "de", "es", "ru", "it", 
            "pt", "pl", "nl", "tr", "ar", "th", "vi", "id", "ms", "hi"
        ]
    
    def get_model_info(self) -> Dict[str, str]:
        """获取模型信息"""
        return {
            "model_size": self.model_size,
            "device": self.device,
            "compute_type": self.compute_type,
            "supported_languages": self.get_supported_languages()
        }
