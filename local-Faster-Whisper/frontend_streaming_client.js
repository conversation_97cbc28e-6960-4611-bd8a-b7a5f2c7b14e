/**
 * Faster-Whisper 前端流式和分块上传客户端
 * 支持实时音频流转录和大文件分块上传
 */

class StreamingWhisperClient {
    constructor(baseUrl = 'http://localhost:8008') {
        this.baseUrl = baseUrl.replace(/\/+$/, '');
        this.sessionId = null;
    }

    /**
     * 创建流式转录会话
     */
    async createSession(options = {}) {
        const formData = new FormData();
        formData.append('task', options.task || 'transcribe');
        if (options.language) {
            formData.append('language', options.language);
        }

        const response = await fetch(`${this.baseUrl}/stream/start`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        if (result.success) {
            this.sessionId = result.data.session_id;
            console.log('✅ 创建流式会话成功:', this.sessionId);
            return this.sessionId;
        } else {
            throw new Error(`创建会话失败: ${result.message}`);
        }
    }

    /**
     * 发送音频块
     */
    async sendAudioChunk(audioData, processNow = false) {
        if (!this.sessionId) {
            throw new Error('会话未创建，请先调用 createSession()');
        }

        const formData = new FormData();
        formData.append('session_id', this.sessionId);
        formData.append('process_now', processNow);
        formData.append('audio_chunk', new Blob([audioData], { type: 'audio/wav' }), 'chunk.wav');

        const response = await fetch(`${this.baseUrl}/stream/chunk`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    /**
     * 处理当前会话的转录
     */
    async processTranscription() {
        if (!this.sessionId) {
            throw new Error('会话未创建');
        }

        const formData = new FormData();
        formData.append('session_id', this.sessionId);

        const response = await fetch(`${this.baseUrl}/stream/process`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    /**
     * 结束流式会话
     */
    async endSession() {
        if (!this.sessionId) {
            return null;
        }

        const formData = new FormData();
        formData.append('session_id', this.sessionId);

        const response = await fetch(`${this.baseUrl}/stream/end`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        this.sessionId = null;
        return result;
    }

    /**
     * 实时音频录制和转录
     */
    async startRealtimeRecording(options = {}) {
        const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        });

        const mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm;codecs=opus'
        });

        const sessionId = await this.createSession(options);
        const audioChunks = [];
        let chunkCount = 0;

        return new Promise((resolve, reject) => {
            mediaRecorder.ondataavailable = async (event) => {
                if (event.data.size > 0) {
                    chunkCount++;
                    audioChunks.push(event.data);

                    try {
                        // 发送音频块
                        const result = await this.sendAudioChunk(
                            event.data, 
                            chunkCount % 3 === 0  // 每3个块处理一次
                        );

                        console.log(`📦 发送音频块 ${chunkCount}:`, result.data.chunk_size, 'bytes');

                        // 如果有转录结果，触发回调
                        if (result.data.processed && options.onPartialResult) {
                            const transcription = result.data.transcription;
                            if (transcription && transcription.transcription) {
                                options.onPartialResult(transcription.transcription);
                            }
                        }
                    } catch (error) {
                        console.error('发送音频块失败:', error);
                        if (options.onError) {
                            options.onError(error);
                        }
                    }
                }
            };

            mediaRecorder.onstop = async () => {
                try {
                    const finalResult = await this.endSession();
                    console.log('✅ 实时录制转录完成');
                    
                    // 停止音频流
                    stream.getTracks().forEach(track => track.stop());
                    
                    resolve(finalResult);
                } catch (error) {
                    reject(error);
                }
            };

            mediaRecorder.onerror = (error) => {
                console.error('录制错误:', error);
                reject(error);
            };

            // 开始录制，每2秒产生一个音频块
            mediaRecorder.start(2000);
            console.log('🎤 开始实时录制...');

            // 返回控制对象
            resolve({
                stop: () => mediaRecorder.stop(),
                sessionId,
                mediaRecorder,
                stream
            });
        });
    }
}


class ChunkedUploadClient {
    constructor(baseUrl = 'http://localhost:8008') {
        this.baseUrl = baseUrl.replace(/\/+$/, '');
    }

    /**
     * 上传大文件并转录
     */
    async uploadLargeFile(file, options = {}) {
        const chunkSize = options.chunkSize || 1024 * 1024; // 1MB
        const totalChunks = Math.ceil(file.size / chunkSize);

        console.log(`📁 准备上传文件: ${file.name}`);
        console.log(`   文件大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
        console.log(`   块大小: ${(chunkSize / (1024 * 1024)).toFixed(2)}MB`);
        console.log(`   总块数: ${totalChunks}`);

        // 1. 开始分块上传
        const uploadId = await this._startUpload(file.name, file.size, chunkSize);

        try {
            // 2. 上传所有块
            for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
                const start = chunkIndex * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const chunk = file.slice(start, end);

                const result = await this._uploadChunk(uploadId, chunkIndex, chunk);
                const progress = result.data.progress;

                console.log(`📦 上传块 ${chunkIndex + 1}/${totalChunks} (${progress.toFixed(1)}%)`);

                // 触发进度回调
                if (options.onProgress) {
                    options.onProgress(chunkIndex + 1, totalChunks, progress);
                }

                // 可选的延迟，避免过快上传
                if (options.delay) {
                    await new Promise(resolve => setTimeout(resolve, options.delay));
                }
            }

            // 3. 完成上传并转录
            console.log('🔄 开始转录...');
            const finalResult = await this._completeUpload(uploadId, options);

            console.log('✅ 分块上传和转录完成');
            return finalResult;

        } catch (error) {
            console.error('❌ 分块上传失败:', error);
            throw error;
        }
    }

    /**
     * 开始分块上传
     */
    async _startUpload(filename, totalSize, chunkSize) {
        const formData = new FormData();
        formData.append('filename', filename);
        formData.append('total_size', totalSize);
        formData.append('chunk_size', chunkSize);

        const response = await fetch(`${this.baseUrl}/upload/start`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        if (result.success) {
            const uploadId = result.data.upload_id;
            console.log('✅ 创建分块上传会话:', uploadId);
            return uploadId;
        } else {
            throw new Error(`创建上传会话失败: ${result.message}`);
        }
    }

    /**
     * 上传单个块
     */
    async _uploadChunk(uploadId, chunkNumber, chunkData) {
        const formData = new FormData();
        formData.append('upload_id', uploadId);
        formData.append('chunk_number', chunkNumber);
        formData.append('chunk_data', chunkData, 'chunk');

        const response = await fetch(`${this.baseUrl}/upload/chunk`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    /**
     * 完成上传并转录
     */
    async _completeUpload(uploadId, options) {
        const formData = new FormData();
        formData.append('upload_id', uploadId);
        formData.append('task', options.task || 'transcribe');
        
        if (options.language) {
            formData.append('language', options.language);
        }

        const response = await fetch(`${this.baseUrl}/upload/complete`, {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }

    /**
     * 获取上传状态
     */
    async getUploadStatus(uploadId) {
        const response = await fetch(`${this.baseUrl}/upload/status/${uploadId}`);
        return await response.json();
    }
}


class RealtimeAudioProcessor {
    constructor(options = {}) {
        this.sampleRate = options.sampleRate || 16000;
        this.channelCount = options.channelCount || 1;
        this.bufferSize = options.bufferSize || 4096;
        this.client = new StreamingWhisperClient(options.baseUrl);
        
        this.audioContext = null;
        this.mediaStream = null;
        this.processor = null;
        this.isRecording = false;
        
        this.audioBuffer = [];
        this.bufferLength = 0;
        this.maxBufferLength = this.sampleRate * 2; // 2秒的音频
    }

    /**
     * 开始实时音频处理
     */
    async startRealtime(options = {}) {
        try {
            // 获取麦克风权限
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.sampleRate,
                    channelCount: this.channelCount,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            });

            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });

            const source = this.audioContext.createMediaStreamSource(this.mediaStream);
            
            // 创建音频处理器
            this.processor = this.audioContext.createScriptProcessor(this.bufferSize, this.channelCount, this.channelCount);
            
            // 创建转录会话
            await this.client.createSession(options);
            
            this.processor.onaudioprocess = async (event) => {
                if (!this.isRecording) return;

                const inputBuffer = event.inputBuffer.getChannelData(0);
                
                // 添加到缓冲区
                this.audioBuffer.push(...inputBuffer);
                this.bufferLength += inputBuffer.length;

                // 当缓冲区达到一定大小时，发送音频块
                if (this.bufferLength >= this.maxBufferLength) {
                    await this._sendBufferedAudio();
                }
            };

            // 连接音频节点
            source.connect(this.processor);
            this.processor.connect(this.audioContext.destination);

            this.isRecording = true;
            console.log('🎤 开始实时音频处理');

            return {
                stop: () => this.stopRealtime(),
                processNow: () => this._processCurrentBuffer(),
                getSessionId: () => this.client.sessionId
            };

        } catch (error) {
            console.error('启动实时音频处理失败:', error);
            throw error;
        }
    }

    /**
     * 停止实时音频处理
     */
    async stopRealtime() {
        this.isRecording = false;

        // 发送剩余的音频数据
        if (this.audioBuffer.length > 0) {
            await this._sendBufferedAudio();
        }

        // 获取最终转录结果
        const finalResult = await this.client.endSession();

        // 清理资源
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }

        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }

        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }

        console.log('🛑 实时音频处理已停止');
        return finalResult;
    }

    /**
     * 发送缓冲的音频数据
     */
    async _sendBufferedAudio() {
        if (this.audioBuffer.length === 0) return;

        // 转换为WAV格式
        const wavBuffer = this._encodeWAV(this.audioBuffer);
        
        try {
            await this.client.sendAudioChunk(wavBuffer);
            console.log(`📦 发送音频块: ${wavBuffer.byteLength} bytes`);
            
            // 清空缓冲区
            this.audioBuffer = [];
            this.bufferLength = 0;
            
        } catch (error) {
            console.error('发送音频块失败:', error);
        }
    }

    /**
     * 处理当前缓冲区的转录
     */
    async _processCurrentBuffer() {
        try {
            const result = await this.client.processTranscription();
            console.log('🔤 处理转录结果:', result);
            return result;
        } catch (error) {
            console.error('处理转录失败:', error);
            throw error;
        }
    }

    /**
     * 编码为WAV格式
     */
    _encodeWAV(samples) {
        const buffer = new ArrayBuffer(44 + samples.length * 2);
        const view = new DataView(buffer);

        // WAV文件头
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + samples.length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, this.channelCount, true);
        view.setUint32(24, this.sampleRate, true);
        view.setUint32(28, this.sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, samples.length * 2, true);

        // 音频数据
        let offset = 44;
        for (let i = 0; i < samples.length; i++) {
            const sample = Math.max(-1, Math.min(1, samples[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }

        return buffer;
    }
}


// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        StreamingWhisperClient,
        ChunkedUploadClient,
        RealtimeAudioProcessor
    };
}

// 浏览器环境下挂载到全局对象
if (typeof window !== 'undefined') {
    window.StreamingWhisperClient = StreamingWhisperClient;
    window.ChunkedUploadClient = ChunkedUploadClient;
    window.RealtimeAudioProcessor = RealtimeAudioProcessor;
}
