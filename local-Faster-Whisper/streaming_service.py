import os
import uuid
import tempfile
import asyncio
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import json
import wave
import numpy as np
from threading import Lock
import threading
import time


class StreamingSession:
    """流式转录会话管理"""
    
    def __init__(self, session_id: str, language: Optional[str] = None, task: str = "transcribe"):
        self.session_id = session_id
        self.language = language
        self.task = task
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.audio_chunks: List[bytes] = []
        self.temp_file_path: Optional[str] = None
        self.transcription_results: List[dict] = []
        self.is_active = True
        self.lock = Lock()
        
    def add_audio_chunk(self, chunk: bytes):
        """添加音频块"""
        with self.lock:
            self.audio_chunks.append(chunk)
            self.last_activity = datetime.now()
    
    def get_combined_audio(self) -> bytes:
        """获取合并的音频数据"""
        with self.lock:
            return b''.join(self.audio_chunks)
    
    def clear_chunks(self):
        """清空音频块"""
        with self.lock:
            self.audio_chunks.clear()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """检查会话是否过期"""
        return datetime.now() - self.last_activity > timedelta(minutes=timeout_minutes)


class StreamingWhisperService:
    """流式 Whisper 转录服务"""
    
    def __init__(self, whisper_service):
        self.whisper_service = whisper_service
        self.sessions: Dict[str, StreamingSession] = {}
        self.session_lock = Lock()
        self.cleanup_thread = None
        self.start_cleanup_thread()
    
    def start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_expired_sessions():
            while True:
                try:
                    self.cleanup_expired_sessions()
                    time.sleep(300)  # 每5分钟清理一次
                except Exception as e:
                    print(f"清理线程错误: {e}")
        
        self.cleanup_thread = threading.Thread(target=cleanup_expired_sessions, daemon=True)
        self.cleanup_thread.start()
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        with self.session_lock:
            expired_sessions = [
                session_id for session_id, session in self.sessions.items()
                if session.is_expired()
            ]
            
            for session_id in expired_sessions:
                session = self.sessions.pop(session_id)
                if session.temp_file_path and os.path.exists(session.temp_file_path):
                    os.remove(session.temp_file_path)
                print(f"清理过期会话: {session_id}")
    
    def create_session(self, language: Optional[str] = None, task: str = "transcribe") -> str:
        """创建新的流式转录会话"""
        session_id = str(uuid.uuid4())
        session = StreamingSession(session_id, language, task)
        
        with self.session_lock:
            self.sessions[session_id] = session
        
        print(f"创建流式会话: {session_id}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[StreamingSession]:
        """获取会话"""
        with self.session_lock:
            return self.sessions.get(session_id)
    
    def add_audio_chunk(self, session_id: str, audio_data: bytes) -> bool:
        """添加音频块到会话"""
        session = self.get_session(session_id)
        if not session or not session.is_active:
            return False
        
        session.add_audio_chunk(audio_data)
        return True
    
    def process_chunk_transcription(self, session_id: str, is_final: bool = False) -> dict:
        """处理音频块转录"""
        session = self.get_session(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        try:
            # 获取当前所有音频数据
            combined_audio = session.get_combined_audio()
            
            if len(combined_audio) == 0:
                return {"error": "没有音频数据"}
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
                temp_file.write(combined_audio)
                temp_path = temp_file.name
            
            try:
                # 执行转录
                result = self.whisper_service.transcribe_audio(
                    temp_path,
                    language=session.language,
                    task=session.task
                )
                
                # 如果是最终转录，添加到结果中
                if is_final:
                    session.transcription_results.append(result)
                
                return {
                    "session_id": session_id,
                    "is_final": is_final,
                    "transcription": result,
                    "chunk_count": len(session.audio_chunks)
                }
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                
        except Exception as e:
            return {"error": f"转录失败: {str(e)}"}
    
    def end_session(self, session_id: str) -> dict:
        """结束会话并返回最终结果"""
        session = self.get_session(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        try:
            # 处理最终转录
            final_result = self.process_chunk_transcription(session_id, is_final=True)
            
            # 标记会话为非活跃
            session.is_active = False
            
            # 从会话列表中移除
            with self.session_lock:
                if session_id in self.sessions:
                    del self.sessions[session_id]
            
            return {
                "session_id": session_id,
                "final_transcription": final_result.get("transcription", {}),
                "total_results": len(session.transcription_results),
                "session_duration": (datetime.now() - session.created_at).total_seconds()
            }
            
        except Exception as e:
            return {"error": f"结束会话失败: {str(e)}"}


class ChunkedUploadService:
    """分块上传服务"""
    
    def __init__(self, whisper_service):
        self.whisper_service = whisper_service
        self.upload_sessions: Dict[str, dict] = {}
        self.session_lock = Lock()
    
    def start_chunked_upload(self, filename: str, total_size: int, chunk_size: int = 1024*1024) -> str:
        """开始分块上传"""
        upload_id = str(uuid.uuid4())
        
        with self.session_lock:
            self.upload_sessions[upload_id] = {
                "filename": filename,
                "total_size": total_size,
                "chunk_size": chunk_size,
                "chunks": {},
                "uploaded_size": 0,
                "created_at": datetime.now(),
                "temp_file": None
            }
        
        return upload_id
    
    def upload_chunk(self, upload_id: str, chunk_number: int, chunk_data: bytes) -> dict:
        """上传单个块"""
        with self.session_lock:
            if upload_id not in self.upload_sessions:
                return {"error": "上传会话不存在"}
            
            session = self.upload_sessions[upload_id]
            session["chunks"][chunk_number] = chunk_data
            session["uploaded_size"] += len(chunk_data)
            
            progress = (session["uploaded_size"] / session["total_size"]) * 100
            
            return {
                "upload_id": upload_id,
                "chunk_number": chunk_number,
                "uploaded_size": session["uploaded_size"],
                "total_size": session["total_size"],
                "progress": round(progress, 2),
                "chunks_received": len(session["chunks"])
            }
    
    def complete_chunked_upload(self, upload_id: str, **transcribe_options) -> dict:
        """完成分块上传并进行转录"""
        with self.session_lock:
            if upload_id not in self.upload_sessions:
                return {"error": "上传会话不存在"}
            
            session = self.upload_sessions[upload_id]
            
        try:
            # 合并所有块
            sorted_chunks = sorted(session["chunks"].items())
            combined_data = b''.join([chunk_data for _, chunk_data in sorted_chunks])
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(session["filename"])[1]) as temp_file:
                temp_file.write(combined_data)
                temp_path = temp_file.name
            
            try:
                # 执行转录
                result = self.whisper_service.transcribe_audio(temp_path, **transcribe_options)
                
                # 清理上传会话
                with self.session_lock:
                    if upload_id in self.upload_sessions:
                        del self.upload_sessions[upload_id]
                
                return {
                    "upload_id": upload_id,
                    "filename": session["filename"],
                    "total_size": session["total_size"],
                    "transcription": result
                }
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                
        except Exception as e:
            return {"error": f"处理失败: {str(e)}"}
    
    def get_upload_status(self, upload_id: str) -> dict:
        """获取上传状态"""
        with self.session_lock:
            if upload_id not in self.upload_sessions:
                return {"error": "上传会话不存在"}
            
            session = self.upload_sessions[upload_id]
            progress = (session["uploaded_size"] / session["total_size"]) * 100
            
            return {
                "upload_id": upload_id,
                "filename": session["filename"],
                "uploaded_size": session["uploaded_size"],
                "total_size": session["total_size"],
                "progress": round(progress, 2),
                "chunks_received": len(session["chunks"]),
                "created_at": session["created_at"].isoformat()
            }
