import os
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class Config:
    """应用程序配置类"""
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # Whisper 模型配置
    WHISPER_MODEL_SIZE: str = os.getenv("WHISPER_MODEL_SIZE", "base")
    WHISPER_DEVICE: str = os.getenv("WHISPER_DEVICE", "auto")
    WHISPER_COMPUTE_TYPE: str = os.getenv("WHISPER_COMPUTE_TYPE", "default")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "26214400"))  # 25MB
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # 模型下载目录
    MODEL_DOWNLOAD_DIR: str = os.getenv("MODEL_DOWNLOAD_DIR", "./models")
    
    # 支持的音频格式
    SUPPORTED_AUDIO_TYPES = {
        "audio/wav", "audio/wave", "audio/x-wav",
        "audio/mp3", "audio/mpeg",
        "audio/mp4", "audio/m4a",
        "audio/flac",
        "audio/ogg", "audio/vorbis",
        "video/mp4", "video/avi", "video/mov"
    }
    
    # 支持的模型大小
    SUPPORTED_MODEL_SIZES = [
        "tiny", "tiny.en",
        "base", "base.en", 
        "small", "small.en",
        "medium", "medium.en",
        "large-v1", "large-v2", "large-v3"
    ]
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否有效"""
        errors = []
        
        # 验证模型大小
        if cls.WHISPER_MODEL_SIZE not in cls.SUPPORTED_MODEL_SIZES:
            errors.append(f"不支持的模型大小: {cls.WHISPER_MODEL_SIZE}")
        
        # 验证设备配置
        if cls.WHISPER_DEVICE not in ["auto", "cpu", "cuda"]:
            errors.append(f"不支持的设备类型: {cls.WHISPER_DEVICE}")
        
        # 验证计算类型
        if cls.WHISPER_COMPUTE_TYPE not in ["default", "int8", "float16"]:
            errors.append(f"不支持的计算类型: {cls.WHISPER_COMPUTE_TYPE}")
        
        # 验证端口号
        if not (1 <= cls.PORT <= 65535):
            errors.append(f"无效的端口号: {cls.PORT}")
        
        # 验证文件大小限制
        if cls.MAX_FILE_SIZE <= 0:
            errors.append(f"无效的文件大小限制: {cls.MAX_FILE_SIZE}")
        
        if errors:
            print("配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("=== Faster-Whisper 服务配置 ===")
        print(f"服务器地址: {cls.HOST}:{cls.PORT}")
        print(f"模型大小: {cls.WHISPER_MODEL_SIZE}")
        print(f"设备类型: {cls.WHISPER_DEVICE}")
        print(f"计算类型: {cls.WHISPER_COMPUTE_TYPE}")
        print(f"最大文件大小: {cls.MAX_FILE_SIZE / (1024*1024):.1f}MB")
        print(f"日志级别: {cls.LOG_LEVEL}")
        print(f"模型目录: {cls.MODEL_DOWNLOAD_DIR}")
        print("=" * 35)


# 全局配置实例
config = Config()


if __name__ == "__main__":
    # 测试配置
    config.print_config()
    is_valid = config.validate_config()
    print(f"配置有效性: {'✓' if is_valid else '✗'}")
