#!/usr/bin/env python3
"""
真正的实时流式音频转录服务
支持 WebSocket 连接，实时处理音频流
"""

import asyncio
import json
import logging
import websockets
import base64
import numpy as np
import tempfile
import os
from typing import Dict, Set, Optional
from datetime import datetime
import threading
import queue
import time
import wave
from whisper_service import WhisperTranscriptionService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RealTimeStreamingService:
    """实时流式音频转录服务"""
    
    def __init__(self, whisper_service: WhisperTranscriptionService):
        self.whisper_service = whisper_service
        self.active_connections: Set[websockets.WebSocketServerProtocol] = set()
        self.connection_sessions: Dict[websockets.WebSocketServerProtocol, dict] = {}
        self.audio_buffers: Dict[websockets.WebSocketServerProtocol, list] = {}
        self.processing_queues: Dict[websockets.WebSocketServerProtocol, queue.Queue] = {}
        self.lock = threading.Lock()
        
        # 音频处理参数
        self.sample_rate = 16000
        self.chunk_duration = 2.0  # 2秒音频块进行处理
        self.chunk_samples = int(self.sample_rate * self.chunk_duration)
        self.min_audio_length = 1.0  # 最小音频长度（秒）
        
        # 启动音频处理线程
        self.start_audio_processing_threads()
    
    def start_audio_processing_threads(self):
        """启动音频处理线程"""
        def audio_processor():
            while True:
                try:
                    # 处理所有活跃连接的音频队列
                    for ws in list(self.active_connections):
                        if ws in self.processing_queues:
                            queue_obj = self.processing_queues[ws]
                            try:
                                # 非阻塞方式获取音频数据
                                audio_data = queue_obj.get_nowait()
                                if audio_data:
                                    self.process_audio_chunk(ws, audio_data)
                                queue_obj.task_done()
                            except queue.Empty:
                                continue
                    
                    time.sleep(0.1)  # 100ms 间隔
                except Exception as e:
                    logger.error(f"音频处理线程错误: {e}")
        
        # 启动处理线程
        thread = threading.Thread(target=audio_processor, daemon=True)
        thread.start()
        logger.info("音频处理线程已启动")
    
    def create_wav_file(self, audio_data: bytes, sample_rate: int = 16000) -> bytes:
        """创建 WAV 格式的音频文件"""
        # 将 Float32 转换为 PCM 16位
        if len(audio_data) % 4 == 0:  # Float32 数据
            audio_array = np.frombuffer(audio_data, dtype=np.float32)
            # 转换为 16位 PCM
            audio_pcm = (audio_array * 32767).astype(np.int16)
        else:
            # 假设已经是 16位 PCM
            audio_pcm = np.frombuffer(audio_data, dtype=np.int16)
        
        # 创建 WAV 文件
        wav_buffer = b''
        
        # WAV 文件头
        wav_buffer += b'RIFF'
        wav_buffer += (36 + len(audio_pcm) * 2).to_bytes(4, 'little')
        wav_buffer += b'WAVE'
        wav_buffer += b'fmt '
        wav_buffer += (16).to_bytes(4, 'little')  # fmt chunk size
        wav_buffer += (1).to_bytes(2, 'little')   # audio format (PCM)
        wav_buffer += (1).to_bytes(2, 'little')   # number of channels
        wav_buffer += sample_rate.to_bytes(4, 'little')  # sample rate
        wav_buffer += (sample_rate * 2).to_bytes(4, 'little')  # byte rate
        wav_buffer += (2).to_bytes(2, 'little')   # block align
        wav_buffer += (16).to_bytes(2, 'little')  # bits per sample
        wav_buffer += b'data'
        wav_buffer += (len(audio_pcm) * 2).to_bytes(4, 'little')
        wav_buffer += audio_pcm.tobytes()
        
        return wav_buffer
    
    def process_audio_chunk(self, ws: websockets.WebSocketServerProtocol, audio_data: bytes):
        """处理音频块"""
        try:
            session = self.connection_sessions.get(ws, {})
            language = session.get('language')
            
            # 创建 WAV 格式的音频
            wav_data = self.create_wav_file(audio_data)
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_file.write(wav_data)
                temp_path = temp_file.name
            
            try:
                # 执行转录
                result = self.whisper_service.transcribe_audio(
                    temp_path,
                    language=language,
                    task='transcribe'
                )
                
                # 发送转录结果
                try:
                    loop = asyncio.get_running_loop()
                    asyncio.run_coroutine_threadsafe(self.send_transcription_result(ws, result), loop)
                except RuntimeError:
                    # 如果没有运行中的事件循环，使用新的事件循环
                    import threading
                    if threading.current_thread() is threading.main_thread():
                        asyncio.run(self.send_transcription_result(ws, result))
                    else:
                        # 在新线程中运行
                        def run_in_thread():
                            asyncio.run(self.send_transcription_result(ws, result))
                        thread = threading.Thread(target=run_in_thread)
                        thread.start()
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            logger.error(f"处理音频块失败: {e}")
            asyncio.create_task(self.send_error(ws, f"音频处理失败: {str(e)}"))
    
    async def send_transcription_result(self, ws: websockets.WebSocketServerProtocol, result: dict):
        """发送转录结果"""
        try:
            message = {
                "type": "transcription",
                "timestamp": datetime.now().isoformat(),
                "data": result
            }
            await ws.send(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送转录结果失败: {e}")
    
    async def send_error(self, ws: websockets.WebSocketServerProtocol, error_message: str):
        """发送错误消息"""
        try:
            message = {
                "type": "error",
                "timestamp": datetime.now().isoformat(),
                "message": error_message
            }
            await ws.send(json.dumps(message, ensure_ascii=False))
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")
    
    async def handle_connection(self, websocket):
        """处理 WebSocket 连接"""
        try:
            # 添加连接
            with self.lock:
                self.active_connections.add(websocket)
                self.connection_sessions[websocket] = {}
                self.audio_buffers[websocket] = []
                self.processing_queues[websocket] = queue.Queue()
            
            logger.info(f"新的 WebSocket 连接: {websocket.remote_address}")
            
            # 发送连接确认
            await websocket.send(json.dumps({
                "type": "connected",
                "timestamp": datetime.now().isoformat(),
                "message": "连接成功",
                "config": {
                    "sample_rate": self.sample_rate,
                    "chunk_duration": self.chunk_duration,
                    "min_audio_length": self.min_audio_length
                }
            }))
            
            # 处理消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(websocket, data)
                except json.JSONDecodeError:
                    await self.send_error(websocket, "无效的 JSON 格式")
                except Exception as e:
                    logger.error(f"处理消息失败: {e}")
                    await self.send_error(websocket, f"消息处理失败: {str(e)}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket 连接关闭: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"WebSocket 连接错误: {e}")
        finally:
            # 清理连接
            await self.cleanup_connection(websocket)
    
    async def handle_message(self, websocket: websockets.WebSocketServerProtocol, data: dict):
        """处理客户端消息"""
        message_type = data.get("type")
        
        if message_type == "config":
            # 更新配置
            session = self.connection_sessions.get(websocket, {})
            if "language" in data:
                session["language"] = data["language"]
            if "task" in data:
                session["task"] = data["task"]
            
            await websocket.send(json.dumps({
                "type": "config_updated",
                "timestamp": datetime.now().isoformat(),
                "config": session
            }))
            
        elif message_type == "audio_data":
            # 接收音频数据
            audio_base64 = data.get("audio_data")
            if audio_base64:
                try:
                    # 解码 base64 音频数据
                    audio_bytes = base64.b64decode(audio_base64)
                    
                    # 添加到音频缓冲区
                    with self.lock:
                        if websocket in self.audio_buffers:
                            self.audio_buffers[websocket].append(audio_bytes)
                            
                            # 检查是否达到处理阈值
                            total_samples = sum(len(chunk) // 4 for chunk in self.audio_buffers[websocket])  # Float32
                            if total_samples >= self.chunk_samples:
                                # 合并音频块
                                combined_audio = b''.join(self.audio_buffers[websocket])
                                
                                # 发送到处理队列
                                if websocket in self.processing_queues:
                                    self.processing_queues[websocket].put(combined_audio)
                                
                                # 清空缓冲区
                                self.audio_buffers[websocket].clear()
                    
                    # 发送确认
                    await websocket.send(json.dumps({
                        "type": "audio_received",
                        "timestamp": datetime.now().isoformat(),
                        "buffer_size": len(self.audio_buffers.get(websocket, [])),
                        "total_bytes": sum(len(chunk) for chunk in self.audio_buffers.get(websocket, []))
                    }))
                    
                except Exception as e:
                    logger.error(f"处理音频数据失败: {e}")
                    await self.send_error(websocket, f"音频数据处理失败: {str(e)}")
            
        elif message_type == "ping":
            # 心跳检测
            await websocket.send(json.dumps({
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            }))
            
        else:
            await self.send_error(websocket, f"未知的消息类型: {message_type}")
    
    async def cleanup_connection(self, websocket: websockets.WebSocketServerProtocol):
        """清理连接资源"""
        with self.lock:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
            if websocket in self.connection_sessions:
                del self.connection_sessions[websocket]
            if websocket in self.audio_buffers:
                del self.audio_buffers[websocket]
            if websocket in self.processing_queues:
                del self.processing_queues[websocket]
        
        logger.info(f"连接资源已清理: {websocket.remote_address}")
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8765):
        """启动 WebSocket 服务器"""
        server = await websockets.serve(
            self.handle_connection,
            host,
            port,
            ping_interval=30,
            ping_timeout=10
        )
        
        logger.info(f"实时流式音频服务已启动: ws://{host}:{port}")
        
        # 保持服务器运行
        await server.wait_closed()


async def main():
    """主函数"""
    try:
        # 初始化 Whisper 服务
        whisper_service = WhisperTranscriptionService(
            model_size="base",
            device="cpu",
            compute_type="int8"
        )
        
        # 创建流式服务
        streaming_service = RealTimeStreamingService(whisper_service)
        
        # 启动 WebSocket 服务器
        await streaming_service.start_server(port=8765)
        
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"服务启动失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
