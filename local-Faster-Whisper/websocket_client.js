/**
 * 真正的实时流式音频转录客户端
 * 基于 WebSocket 实现实时音频流处理
 */

class RealTimeWhisperClient {
    constructor(options = {}) {
        this.wsUrl = options.wsUrl || 'ws://localhost:8765';
        this.sampleRate = options.sampleRate || 16000;
        this.bufferSize = options.bufferSize || 4096;
        this.language = options.language || 'auto';
        
        this.ws = null;
        this.audioContext = null;
        this.mediaStream = null;
        this.processor = null;
        this.isRecording = false;
        this.isConnected = false;
        
        // 回调函数
        this.onTranscription = options.onTranscription || null;
        this.onError = options.onError || null;
        this.onConnected = options.onConnected || null;
        this.onDisconnected = options.onDisconnected || null;
        
        // 音频缓冲区
        this.audioBuffer = [];
        this.bufferLength = 0;
        this.sendInterval = 500; // 500ms 发送一次音频数据
        
        this.initializeWebSocket();
    }
    
    /**
     * 初始化 WebSocket 连接
     */
    initializeWebSocket() {
        try {
            this.ws = new WebSocket(this.wsUrl);
            
            this.ws.onopen = () => {
                console.log('✅ WebSocket 连接成功');
                this.isConnected = true;
                
                // 发送配置
                this.sendConfig();
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('解析消息失败:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('❌ WebSocket 连接关闭');
                this.isConnected = false;
                if (this.onDisconnected) {
                    this.onDisconnected();
                }
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket 错误:', error);
                if (this.onError) {
                    this.onError(error);
                }
            };
            
        } catch (error) {
            console.error('WebSocket 初始化失败:', error);
            if (this.onError) {
                this.onError(error);
            }
        }
    }
    
    /**
     * 发送配置信息
     */
    sendConfig() {
        if (this.ws && this.isConnected) {
            const config = {
                type: 'config',
                language: this.language,
                task: 'transcribe'
            };
            this.ws.send(JSON.stringify(config));
        }
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        switch (data.type) {
            case 'connected':
                console.log('🔗 服务器连接确认:', data.message);
                if (this.onConnected) {
                    this.onConnected(data);
                }
                break;
                
            case 'config_updated':
                console.log('⚙️ 配置已更新:', data.config);
                break;
                
            case 'transcription':
                console.log('🔤 转录结果:', data.data.text);
                if (this.onTranscription) {
                    this.onTranscription(data.data);
                }
                break;
                
            case 'audio_received':
                // 音频接收确认，可以用于调试
                console.debug('📦 音频已接收:', data.buffer_size, '个块');
                break;
                
            case 'error':
                console.error('❌ 服务器错误:', data.message);
                if (this.onError) {
                    this.onError(new Error(data.message));
                }
                break;
                
            case 'pong':
                console.debug('🏓 心跳响应');
                break;
                
            default:
                console.warn('未知消息类型:', data.type);
        }
    }
    
    /**
     * 开始实时录音转录
     */
    async startRecording() {
        if (!this.isConnected) {
            throw new Error('WebSocket 未连接');
        }
        
        if (this.isRecording) {
            console.warn('录音已在进行中');
            return;
        }
        
        try {
            // 获取麦克风权限
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.sampleRate,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });
            
            const source = this.audioContext.createMediaStreamSource(this.mediaStream);
            
            // 创建音频处理器
            this.processor = this.audioContext.createScriptProcessor(
                this.bufferSize, 
                1, // 输入通道数
                1  // 输出通道数
            );
            
            this.processor.onaudioprocess = (event) => {
                if (!this.isRecording) return;
                
                const inputBuffer = event.inputBuffer.getChannelData(0);
                
                // 添加到音频缓冲区
                this.audioBuffer.push(...inputBuffer);
                this.bufferLength += inputBuffer.length;
                
                // 当缓冲区达到一定大小时，发送音频数据
                const targetSamples = this.sampleRate * (this.sendInterval / 1000);
                if (this.bufferLength >= targetSamples) {
                    this.sendAudioData();
                }
            };
            
            // 连接音频节点
            source.connect(this.processor);
            this.processor.connect(this.audioContext.destination);
            
            this.isRecording = true;
            console.log('🎤 开始实时录音转录');
            
        } catch (error) {
            console.error('启动录音失败:', error);
            throw error;
        }
    }
    
    /**
     * 停止录音
     */
    async stopRecording() {
        this.isRecording = false;
        
        // 发送剩余的音频数据
        if (this.audioBuffer.length > 0) {
            this.sendAudioData();
        }
        
        // 清理音频资源
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }
        
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }
        
        console.log('🛑 录音已停止');
    }
    
    /**
     * 发送音频数据到服务器
     */
    sendAudioData() {
        if (!this.isConnected || !this.ws || this.audioBuffer.length === 0) {
            return;
        }
        
        try {
            // 转换为 Float32Array
            const audioData = new Float32Array(this.audioBuffer);
            
            // 转换为 base64
            const audioBytes = new Uint8Array(audioData.buffer);
            const audioBase64 = btoa(String.fromCharCode(...audioBytes));
            
            // 发送音频数据
            const message = {
                type: 'audio_data',
                audio_data: audioBase64,
                sample_rate: this.sampleRate,
                samples: audioData.length
            };
            
            this.ws.send(JSON.stringify(message));
            
            // 清空缓冲区
            this.audioBuffer = [];
            this.bufferLength = 0;
            
            console.debug(`📤 发送音频数据: ${audioData.length} 采样`);
            
        } catch (error) {
            console.error('发送音频数据失败:', error);
        }
    }
    
    /**
     * 设置语言
     */
    setLanguage(language) {
        this.language = language;
        if (this.isConnected) {
            this.sendConfig();
        }
    }
    
    /**
     * 发送心跳
     */
    sendPing() {
        if (this.isConnected && this.ws) {
            this.ws.send(JSON.stringify({ type: 'ping' }));
        }
    }
    
    /**
     * 关闭连接
     */
    disconnect() {
        if (this.isRecording) {
            this.stopRecording();
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.isConnected = false;
    }
    
    /**
     * 重新连接
     */
    reconnect() {
        this.disconnect();
        setTimeout(() => {
            this.initializeWebSocket();
        }, 1000);
    }
    
    /**
     * 检查连接状态
     */
    isConnectedToServer() {
        return this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN;
    }
}


/**
 * 实时音频转录管理器
 * 提供更高级的功能和错误处理
 */
class RealtimeTranscriptionManager {
    constructor(options = {}) {
        this.client = null;
        this.options = options;
        this.isActive = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 2000;
        
        // 事件回调
        this.onTranscription = options.onTranscription || null;
        this.onError = options.onError || null;
        this.onStatusChange = options.onStatusChange || null;
        
        // 转录历史
        this.transcriptionHistory = [];
    }
    
    /**
     * 开始实时转录
     */
    async start() {
        if (this.isActive) {
            console.warn('实时转录已在运行');
            return;
        }
        
        try {
            this.isActive = true;
            
            // 创建客户端
            this.client = new RealTimeWhisperClient({
                ...this.options,
                onTranscription: (result) => {
                    this.handleTranscription(result);
                },
                onError: (error) => {
                    this.handleError(error);
                },
                onConnected: () => {
                    this.reconnectAttempts = 0;
                    this.updateStatus('connected');
                },
                onDisconnected: () => {
                    this.updateStatus('disconnected');
                    if (this.isActive) {
                        this.handleReconnect();
                    }
                }
            });
            
            // 等待连接建立
            await this.waitForConnection();
            
            // 开始录音
            await this.client.startRecording();
            
            this.updateStatus('recording');
            
        } catch (error) {
            this.isActive = false;
            this.handleError(error);
            throw error;
        }
    }
    
    /**
     * 停止实时转录
     */
    async stop() {
        this.isActive = false;
        
        if (this.client) {
            await this.client.stopRecording();
            this.client.disconnect();
            this.client = null;
        }
        
        this.updateStatus('stopped');
    }
    
    /**
     * 等待连接建立
     */
    waitForConnection(timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkConnection = () => {
                if (this.client && this.client.isConnectedToServer()) {
                    resolve();
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('连接超时'));
                } else {
                    setTimeout(checkConnection, 100);
                }
            };
            
            checkConnection();
        });
    }
    
    /**
     * 处理转录结果
     */
    handleTranscription(result) {
        // 添加到历史记录
        this.transcriptionHistory.push({
            timestamp: new Date(),
            text: result.text,
            language: result.language,
            confidence: result.language_probability
        });
        
        // 触发回调
        if (this.onTranscription) {
            this.onTranscription(result);
        }
    }
    
    /**
     * 处理错误
     */
    handleError(error) {
        console.error('实时转录错误:', error);
        
        if (this.onError) {
            this.onError(error);
        }
    }
    
    /**
     * 处理重连
     */
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            this.stop();
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (this.isActive && this.client) {
                this.client.reconnect();
            }
        }, this.reconnectDelay);
    }
    
    /**
     * 更新状态
     */
    updateStatus(status) {
        console.log(`状态变更: ${status}`);
        
        if (this.onStatusChange) {
            this.onStatusChange(status);
        }
    }
    
    /**
     * 获取转录历史
     */
    getTranscriptionHistory() {
        return this.transcriptionHistory;
    }
    
    /**
     * 清空转录历史
     */
    clearHistory() {
        this.transcriptionHistory = [];
    }
    
    /**
     * 设置语言
     */
    setLanguage(language) {
        if (this.client) {
            this.client.setLanguage(language);
        }
    }
}


// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        RealTimeWhisperClient,
        RealtimeTranscriptionManager
    };
}

// 浏览器环境下挂载到全局对象
if (typeof window !== 'undefined') {
    window.RealTimeWhisperClient = RealTimeWhisperClient;
    window.RealtimeTranscriptionManager = RealtimeTranscriptionManager;
}
