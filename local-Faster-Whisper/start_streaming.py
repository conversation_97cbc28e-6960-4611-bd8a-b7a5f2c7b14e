#!/usr/bin/env python3
"""
启动完整的 Faster-Whisper 服务
包括HTTP API服务和WebSocket流式服务
"""

import asyncio
import threading
import logging
import os
import sys
import signal
from multiprocessing import Process
import uvicorn
from real_time_streaming import main as streaming_main
from whisper_service import WhisperTranscriptionService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def start_http_server():
    """启动HTTP API服务器"""
    try:
        logger.info("🌐 启动 HTTP API 服务器...")
        
        # 从环境变量获取配置
        host = os.getenv("HOST", "0.0.0.0")
        port = int(os.getenv("PORT", "8008"))
        
        uvicorn.run(
            "app:app",
            host=host,
            port=port,
            reload=False,
            workers=1,
            log_level="info"
        )
    except Exception as e:
        logger.error(f"HTTP 服务器启动失败: {e}")


def start_websocket_server():
    """启动WebSocket流式服务器"""
    try:
        logger.info("🔌 启动 WebSocket 流式服务器...")
        asyncio.run(streaming_main())
    except Exception as e:
        logger.error(f"WebSocket 服务器启动失败: {e}")


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info("收到停止信号，正在关闭服务...")
    sys.exit(0)


def main():
    """主函数"""
    logger.info("🚀 启动 Faster-Whisper 完整服务")
    logger.info("=" * 60)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动HTTP服务器进程
        http_process = Process(target=start_http_server)
        http_process.start()
        
        # 启动WebSocket服务器进程
        websocket_process = Process(target=start_websocket_server)
        websocket_process.start()
        
        logger.info("📋 服务信息:")
        logger.info("   HTTP API: http://localhost:8008")
        logger.info("   WebSocket: ws://localhost:8765")
        logger.info("   API 文档: http://localhost:8008/docs")
        logger.info("   演示页面: demo.html")
        logger.info("=" * 60)
        logger.info("🎤 实时转录: 使用 WebSocket 连接 ws://localhost:8765")
        logger.info("📁 文件转录: 使用 HTTP API http://localhost:8008/transcribe")
        logger.info("🛑 按 Ctrl+C 停止所有服务")
        
        # 等待进程结束
        try:
            http_process.join()
            websocket_process.join()
        except KeyboardInterrupt:
            logger.info("正在停止服务...")
            
            # 终止进程
            if http_process.is_alive():
                http_process.terminate()
                http_process.join(timeout=5)
                
            if websocket_process.is_alive():
                websocket_process.terminate()
                websocket_process.join(timeout=5)
                
            logger.info("所有服务已停止")
            
    except Exception as e:
        logger.error(f"服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
