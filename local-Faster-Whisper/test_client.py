#!/usr/bin/env python3
"""
Faster-Whisper 服务测试客户端
用于测试语音识别服务是否正常工作
"""

import requests
import json
import time
import os
from pathlib import Path


class WhisperClient:
    """Whisper 服务客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8008"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 服务健康: {result}")
                return True
            else:
                print(f"❌ 服务不健康: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_info(self) -> dict:
        """获取模型信息"""
        try:
            response = self.session.get(f"{self.base_url}/info")
            response.raise_for_status()
            result = response.json()
            if result["success"]:
                print("📋 模型信息:")
                data = result["data"]
                print(f"   模型大小: {data['model_size']}")
                print(f"   设备类型: {data['device']}")
                print(f"   计算类型: {data['compute_type']}")
                return data
            else:
                print(f"❌ 获取信息失败: {result}")
                return {}
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return {}
    
    def get_languages(self) -> list:
        """获取支持的语言"""
        try:
            response = self.session.get(f"{self.base_url}/languages")
            response.raise_for_status()
            result = response.json()
            if result["success"]:
                languages = result["data"]["languages"]
                print(f"🌍 支持 {len(languages)} 种语言")
                return languages
            else:
                print(f"❌ 获取语言列表失败: {result}")
                return []
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return []
    
    def transcribe_file(self, 
                       file_path: str, 
                       language: str = None,
                       task: str = "transcribe") -> dict:
        """转录音频文件"""
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return {}
        
        try:
            print(f"🎵 正在转录: {file_path}")
            start_time = time.time()
            
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {
                    'task': task,
                }
                if language:
                    data['language'] = language
                
                response = self.session.post(
                    f"{self.base_url}/transcribe", 
                    files=files, 
                    data=data,
                    timeout=300  # 5分钟超时
                )
            
            response.raise_for_status()
            result = response.json()
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result["success"]:
                data = result["data"]
                print(f"✅ 转录成功 (耗时: {duration:.2f}s)")
                print(f"   检测语言: {data['language']} (置信度: {data['language_probability']:.2f})")
                print(f"   音频时长: {data['duration']:.2f}s")
                print(f"   转录文本: {data['text']}")
                print(f"   片段数量: {len(data['segments'])}")
                return data
            else:
                print(f"❌ 转录失败: {result['message']}")
                return {}
                
        except Exception as e:
            print(f"❌ 转录请求失败: {e}")
            return {}
    
    def test_api_endpoints(self):
        """测试所有 API 端点"""
        print("🧪 测试 API 端点...")
        print("=" * 50)
        
        # 测试健康检查
        print("1. 健康检查")
        if not self.health_check():
            print("服务未启动或不可用，请先启动服务")
            return False
        
        print()
        
        # 测试模型信息
        print("2. 模型信息")
        self.get_info()
        
        print()
        
        # 测试语言列表
        print("3. 支持的语言")
        languages = self.get_languages()
        
        print()
        
        return True


def create_test_audio():
    """创建测试音频文件说明"""
    print("📝 创建测试音频文件:")
    print("   由于版权原因，本脚本不包含测试音频文件")
    print("   请准备一个音频文件 (wav, mp3, m4a 等格式)")
    print("   然后使用以下命令测试:")
    print("   python test_client.py --file your_audio.wav")
    print()


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Faster-Whisper 服务测试客户端")
    parser.add_argument("--url", default="http://localhost:8000", help="服务地址")
    parser.add_argument("--file", help="要转录的音频文件路径")
    parser.add_argument("--language", help="语言代码 (如: zh, en)")
    parser.add_argument("--task", default="transcribe", choices=["transcribe", "translate"], help="任务类型")
    
    args = parser.parse_args()
    
    print("🎙️ Faster-Whisper 服务测试客户端")
    print("=" * 50)
    
    client = WhisperClient(args.url)
    
    if args.file:
        # 测试转录指定文件
        if client.health_check():
            print()
            client.transcribe_file(args.file, args.language, args.task)
    else:
        # 执行完整的 API 测试
        if client.test_api_endpoints():
            create_test_audio()


if __name__ == "__main__":
    main()
