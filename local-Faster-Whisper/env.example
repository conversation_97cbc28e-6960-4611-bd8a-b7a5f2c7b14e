# Faster-Whisper 服务配置文件
# 复制此文件为 .env 并根据需要修改配置

# 服务器配置
HOST=0.0.0.0
PORT=8000

# Whisper 模型配置
# 模型大小: tiny, base, small, medium, large-v2, large-v3
WHISPER_MODEL_SIZE=base

# 设备配置: auto, cpu, cuda
WHISPER_DEVICE=auto

# 计算类型: default, int8, float16
WHISPER_COMPUTE_TYPE=default

# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 文件上传限制 (字节)
MAX_FILE_SIZE=26214400

# 模型下载目录
MODEL_DOWNLOAD_DIR=./models
