# 🚀 快速启动指南

## 1️⃣ 安装依赖

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\\Scripts\\activate   # Windows

# 安装依赖包
pip install -r requirements.txt
```

## 2️⃣ 配置服务

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置（可选）
# nano .env
```

## 3️⃣ 启动服务

```bash
# 方式 1: 使用启动脚本
python start.py

# 方式 2: 使用 Shell 脚本
./run.sh

# 方式 3: 开发模式（自动重载）
python start.py --reload

# 方式 4: 指定配置
python start.py --model base --device cpu --port 8080
```

## 4️⃣ 测试服务

```bash
# 测试 API 端点
python test_client.py

# 转录音频文件
python test_client.py --file your_audio.wav --language zh
```

## 5️⃣ 使用 API

### 浏览器访问

- 服务首页: http://localhost:8000
- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### curl 命令

```bash
# 健康检查
curl http://localhost:8000/health

# 上传音频转录
curl -X POST http://localhost:8000/transcribe \\
  -F "file=@audio.wav" \\
  -F "language=zh"
```

### Python 客户端

```python
import requests

# 上传文件转录
with open("audio.wav", "rb") as f:
    files = {"file": f}
    data = {"language": "zh"}
    response = requests.post("http://localhost:8000/transcribe", 
                           files=files, data=data)
    
result = response.json()
print(result["data"]["text"])
```

## 🔧 常用配置

### 小内存设备 (4GB 以下)
```bash
python start.py --model tiny --device cpu
```

### GPU 加速
```bash
python start.py --model base --device cuda
```

### 高精度转录
```bash
python start.py --model large-v3 --device cuda
```

---

**提示**: 首次启动会下载模型文件，请耐心等待！
