<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faster-Whisper 流式转录和分块上传演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .tab-container {
            margin: 20px 0;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: #f8f9fa;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            background: #007bff;
            color: white;
            border-bottom-color: #007bff;
        }

        .tab-content {
            display: none;
            padding: 20px 0;
        }

        .tab-content.active {
            display: block;
        }

        .input-group {
            margin: 15px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .button-group {
            margin: 20px 0;
            text-align: center;
        }

        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background 0.3s;
        }

        button:hover:not(:disabled) {
            background: #0056b3;
        }

        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s;
        }

        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }

        .status.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .status.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .status.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result-container {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }

        .result-text {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }

        .recording-indicator {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .recording-dot {
            width: 20px;
            height: 20px;
            background-color: #dc3545;
            border-radius: 50%;
            animation: pulse 1s infinite;
            margin: 0 auto 10px;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }

            100% {
                opacity: 1;
            }
        }

        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .upload-area:hover {
            border-color: #007bff;
        }

        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>

<body>
    <h1>🎙️ Faster-Whisper 流式转录和分块上传演示</h1>

    <div class="container">
        <div class="tab-container">
            <div class="tabs">
                <button class="tab active" onclick="switchTab('realtime')">实时录音转录</button>
                <button class="tab" onclick="switchTab('streaming')">流式音频转录</button>
                <button class="tab" onclick="switchTab('chunked')">分块文件上传</button>
            </div>

            <!-- 实时录音转录 -->
            <div id="realtime" class="tab-content active">
                <h2>🎤 实时录音转录</h2>
                <p>点击开始录音，系统将实时转录您的语音：</p>

                <div class="input-group">
                    <label>语言选择:</label>
                    <select id="realtimeLanguage">
                        <option value="">自动检测</option>
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                    </select>
                </div>

                <div class="button-group">
                    <button id="startRecording" onclick="startRealtimeRecording()">开始录音</button>
                    <button id="stopRecording" onclick="stopRealtimeRecording()" disabled>停止录音</button>
                </div>

                <div class="recording-indicator" id="recordingIndicator">
                    <div class="recording-dot"></div>
                    <p>正在录音中...</p>
                </div>

                <div class="status" id="realtimeStatus"></div>

                <div class="result-container" id="realtimeResult">
                    <h3>转录结果:</h3>
                    <div class="result-text" id="realtimeText">等待转录结果...</div>
                </div>
            </div>

            <!-- 流式音频转录 -->
            <div id="streaming" class="tab-content">
                <h2>🔄 流式音频转录</h2>
                <p>上传音频文件，使用流式方式进行转录：</p>

                <div class="input-group">
                    <label>选择音频文件:</label>
                    <input type="file" id="streamingFile" accept="audio/*">
                </div>

                <div class="input-group">
                    <label>语言选择:</label>
                    <select id="streamingLanguage">
                        <option value="">自动检测</option>
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                    </select>
                </div>

                <div class="button-group">
                    <button onclick="startStreamingTranscription()">开始流式转录</button>
                </div>

                <div class="progress-container" id="streamingProgress">
                    <label>处理进度:</label>
                    <div class="progress-bar">
                        <div class="progress-fill" id="streamingProgressFill"></div>
                    </div>
                    <div id="streamingProgressText">0%</div>
                </div>

                <div class="status" id="streamingStatus"></div>

                <div class="result-container" id="streamingResult">
                    <h3>转录结果:</h3>
                    <div class="result-text" id="streamingText">等待转录结果...</div>
                </div>
            </div>

            <!-- 分块文件上传 -->
            <div id="chunked" class="tab-content">
                <h2>📦 分块文件上传</h2>
                <p>上传大文件，支持断点续传和进度显示：</p>

                <div class="upload-area" id="uploadArea" onclick="document.getElementById('chunkedFile').click()">
                    <p>拖拽文件到这里，或点击选择文件</p>
                    <input type="file" id="chunkedFile" accept="audio/*" style="display: none;">
                </div>

                <div class="input-group">
                    <label>语言选择:</label>
                    <select id="chunkedLanguage">
                        <option value="">自动检测</option>
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                        <option value="ja">日文</option>
                        <option value="ko">韩文</option>
                    </select>
                </div>

                <div class="input-group">
                    <label>块大小 (MB):</label>
                    <select id="chunkSize">
                        <option value="1048576">1 MB</option>
                        <option value="2097152">2 MB</option>
                        <option value="5242880">5 MB</option>
                        <option value="10485760">10 MB</option>
                    </select>
                </div>

                <div class="button-group">
                    <button onclick="startChunkedUpload()">开始分块上传</button>
                </div>

                <div class="progress-container" id="chunkedProgress">
                    <label>上传进度:</label>
                    <div class="progress-bar">
                        <div class="progress-fill" id="chunkedProgressFill"></div>
                    </div>
                    <div id="chunkedProgressText">0% (0/0 块)</div>
                </div>

                <div class="status" id="chunkedStatus"></div>

                <div class="result-container" id="chunkedResult">
                    <h3>转录结果:</h3>
                    <div class="result-text" id="chunkedText">等待转录结果...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入客户端库 -->
    <script src="frontend_streaming_client.js"></script>
    <script src="websocket_client.js"></script>

    <script>
        // 全局变量
        let realtimeProcessor = null;
        let currentRecording = null;
        let realtimeManager = null;

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的活跃状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 设置对应标签为活跃状态
            event.target.classList.add('active');
        }

        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const statusElement = document.getElementById(elementId);
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
        }

        // 隐藏状态消息
        function hideStatus(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        // 显示结果
        function showResult(containerId, textId, result) {
            document.getElementById(containerId).style.display = 'block';
            document.getElementById(textId).textContent = result;
        }

        // 更新进度条
        function updateProgress(fillId, textId, progress, current = 0, total = 0) {
            document.getElementById(fillId).style.width = `${progress}%`;
            if (current && total) {
                document.getElementById(textId).textContent = `${progress.toFixed(1)}% (${current}/${total} 块)`;
            } else {
                document.getElementById(textId).textContent = `${progress.toFixed(1)}%`;
            }
        }

        // 实时录音转录 - 真正的流式处理
        async function startRealtimeRecording() {
            try {
                const language = document.getElementById('realtimeLanguage').value;

                showStatus('realtimeStatus', '正在连接流式转录服务...', 'info');

                // 创建实时转录管理器
                realtimeManager = new RealtimeTranscriptionManager({
                    wsUrl: 'ws://localhost:8765',
                    language: language || 'auto',
                    sampleRate: 16000,
                    onTranscription: (result) => {
                        // 实时显示转录结果
                        const currentText = document.getElementById('realtimeText').textContent;
                        const newText = result.text;

                        // 如果是新的转录结果，追加显示
                        if (newText && newText.trim() && !currentText.includes(newText)) {
                            const displayText = currentText === '等待转录结果...' ? newText : currentText + ' ' + newText;
                            document.getElementById('realtimeText').textContent = displayText;
                        }

                        console.log('🔤 实时转录:', result);
                    },
                    onError: (error) => {
                        console.error('转录错误:', error);
                        showStatus('realtimeStatus', `转录错误: ${error.message}`, 'error');
                    },
                    onStatusChange: (status) => {
                        console.log('状态变更:', status);

                        switch (status) {
                            case 'connected':
                                showStatus('realtimeStatus', '已连接到服务器，准备开始录音...', 'info');
                                break;
                            case 'recording':
                                showStatus('realtimeStatus', '正在录音，请开始说话...', 'success');
                                break;
                            case 'disconnected':
                                showStatus('realtimeStatus', '连接已断开', 'error');
                                break;
                            case 'stopped':
                                showStatus('realtimeStatus', '录音已停止', 'info');
                                break;
                        }
                    }
                });

                // 开始实时转录
                await realtimeManager.start();

                // 更新UI
                document.getElementById('startRecording').disabled = true;
                document.getElementById('stopRecording').disabled = false;
                document.getElementById('recordingIndicator').style.display = 'block';
                document.getElementById('realtimeResult').style.display = 'block';
                document.getElementById('realtimeText').textContent = '等待转录结果...';

            } catch (error) {
                console.error('启动实时转录失败:', error);
                showStatus('realtimeStatus', `启动失败: ${error.message}`, 'error');
            }
        }

        async function stopRealtimeRecording() {
            if (realtimeManager) {
                try {
                    showStatus('realtimeStatus', '正在停止录音...', 'info');

                    // 停止实时转录
                    await realtimeManager.stop();

                    // 获取完整的转录历史
                    const history = realtimeManager.getTranscriptionHistory();
                    if (history.length > 0) {
                        const fullText = history.map(item => item.text).join(' ');
                        document.getElementById('realtimeText').textContent = fullText;
                        showStatus('realtimeStatus', `录音完成！共转录 ${history.length} 段语音`, 'success');
                    } else {
                        showStatus('realtimeStatus', '录音已停止，但未获得转录结果', 'error');
                    }

                    // 更新UI
                    document.getElementById('startRecording').disabled = false;
                    document.getElementById('stopRecording').disabled = true;
                    document.getElementById('recordingIndicator').style.display = 'none';

                    realtimeManager = null;

                } catch (error) {
                    console.error('停止录音失败:', error);
                    showStatus('realtimeStatus', `停止失败: ${error.message}`, 'error');
                }
            }
        }

        // 流式音频转录
        async function startStreamingTranscription() {
            const fileInput = document.getElementById('streamingFile');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('streamingStatus', '请先选择音频文件', 'error');
                return;
            }

            try {
                const language = document.getElementById('streamingLanguage').value;
                const client = new StreamingWhisperClient('http://localhost:8008');

                showStatus('streamingStatus', '正在开始流式转录...', 'info');
                document.getElementById('streamingProgress').style.display = 'block';

                // 创建会话
                await client.createSession({ language: language || undefined });

                // 将文件分块
                const chunkSize = 1024 * 1024; // 1MB
                const totalChunks = Math.ceil(file.size / chunkSize);

                // 发送音频块
                for (let i = 0; i < totalChunks; i++) {
                    const start = i * chunkSize;
                    const end = Math.min(start + chunkSize, file.size);
                    const chunk = file.slice(start, end);

                    const processNow = (i + 1) % 3 === 0; // 每3个块处理一次
                    const result = await client.sendAudioChunk(chunk, processNow);

                    const progress = ((i + 1) / totalChunks) * 100;
                    updateProgress('streamingProgressFill', 'streamingProgressText', progress);

                    // 显示中间结果
                    if (processNow && result.data.processed && result.data.transcription) {
                        const text = result.data.transcription.transcription?.text || '处理中...';
                        showResult('streamingResult', 'streamingText', text);
                    }
                }

                // 获取最终结果
                showStatus('streamingStatus', '正在获取最终转录结果...', 'info');
                const finalResult = await client.endSession();

                if (finalResult.data && finalResult.data.final_transcription) {
                    const transcription = finalResult.data.final_transcription;
                    showResult('streamingResult', 'streamingText', transcription.text || '无转录结果');
                    showStatus('streamingStatus', '流式转录完成！', 'success');
                } else {
                    showStatus('streamingStatus', '转录完成，但未获得结果', 'error');
                }

            } catch (error) {
                console.error('流式转录失败:', error);
                showStatus('streamingStatus', `流式转录失败: ${error.message}`, 'error');
            }
        }

        // 分块文件上传
        async function startChunkedUpload() {
            const fileInput = document.getElementById('chunkedFile');
            const file = fileInput.files[0];

            if (!file) {
                showStatus('chunkedStatus', '请先选择音频文件', 'error');
                return;
            }

            try {
                const language = document.getElementById('chunkedLanguage').value;
                const chunkSize = parseInt(document.getElementById('chunkSize').value);

                const client = new ChunkedUploadClient('http://localhost:8008');

                showStatus('chunkedStatus', '正在开始分块上传...', 'info');
                document.getElementById('chunkedProgress').style.display = 'block';

                const result = await client.uploadLargeFile(file, {
                    language: language || undefined,
                    chunkSize: chunkSize,
                    onProgress: (current, total, progress) => {
                        updateProgress('chunkedProgressFill', 'chunkedProgressText', progress, current, total);
                        showStatus('chunkedStatus', `正在上传: ${progress.toFixed(1)}%`, 'info');
                    }
                });

                if (result.success && result.data.transcription) {
                    const transcription = result.data.transcription;
                    showResult('chunkedResult', 'chunkedText', transcription.text || '无转录结果');
                    showStatus('chunkedStatus', '分块上传和转录完成！', 'success');
                } else {
                    showStatus('chunkedStatus', '上传完成，但转录失败', 'error');
                }

            } catch (error) {
                console.error('分块上传失败:', error);
                showStatus('chunkedStatus', `分块上传失败: ${error.message}`, 'error');
            }
        }

        // 文件拖拽处理
        const uploadArea = document.getElementById('uploadArea');
        const chunkedFileInput = document.getElementById('chunkedFile');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                chunkedFileInput.files = files;
                uploadArea.innerHTML = `<p>已选择文件: ${files[0].name}</p>`;
            }
        });

        // 文件选择处理
        chunkedFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                uploadArea.innerHTML = `<p>已选择文件: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)} MB)</p>`;
            }
        });

        // 页面加载完成时检查服务连接
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('http://localhost:8008/health');
                const result = await response.json();

                if (result.status === 'healthy') {
                    console.log('✅ 服务连接正常');
                } else {
                    console.warn('⚠️ 服务状态异常');
                }
            } catch (error) {
                console.error('❌ 无法连接到服务，请确保服务已启动在端口8008');
                alert('无法连接到语音转录服务，请确保服务已启动！');
            }
        });
    </script>
</body>

</html>