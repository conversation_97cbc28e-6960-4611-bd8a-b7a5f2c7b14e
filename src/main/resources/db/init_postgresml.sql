-- PostgresML 向量数据库初始化脚本
-- 用于K12教育RAG AI聊天项目
-- 支持向量检索和教育内容分类存储
-- 向量维度：1536（与embedding模型维度一致）

-- 0. 清理旧表和函数
DROP TABLE IF EXISTS math_rag_knowledge CASCADE;
DROP TABLE IF EXISTS embeddings CASCADE;
DROP FUNCTION IF EXISTS search_knowledge CASCADE;
DROP FUNCTION IF EXISTS add_knowledge CASCADE;
DROP FUNCTION IF EXISTS search_embeddings CASCADE;
DROP FUNCTION IF EXISTS add_embedding CASCADE;

-- 1. 创建 pgvector 扩展（如果还没有安装）
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. 创建K12教育知识库表（1536维向量）
CREATE TABLE IF NOT EXISTS math_rag_knowledge (
    id SERIAL PRIMARY KEY,
    source TEXT,                    -- 数据来源（PDF名称、网页URL等）
    content TEXT NOT NULL,          -- 文本内容（去掉排版，只保留可读文本）
    type TEXT,                      -- 内容类型（题目/讲解/概念/公式/例题）
    grade TEXT,                     -- 年级（P5, Sec2, O-Level等）
    topic TEXT,                     -- 题型/知识点（代数、几何、数列、函数等）
    difficulty TEXT,                -- 简单/中等/难
    metadata JSONB,                 -- 额外信息（原始文件页码、章节、考试年份等）
    embedding vector(1536),         -- 向量表示（用于检索）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建向量相似度搜索索引（HNSW索引）
CREATE INDEX IF NOT EXISTS math_rag_knowledge_embedding_idx
ON math_rag_knowledge USING hnsw (embedding vector_cosine_ops);


-- 3. 创建其他索引
-- 创建元数据索引
CREATE INDEX IF NOT EXISTS math_rag_knowledge_metadata_idx
ON math_rag_knowledge USING GIN (metadata);

-- 创建教育相关字段索引
CREATE INDEX IF NOT EXISTS math_rag_knowledge_grade_idx
ON math_rag_knowledge (grade);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_topic_idx
ON math_rag_knowledge (topic);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_type_idx
ON math_rag_knowledge (type);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_difficulty_idx
ON math_rag_knowledge (difficulty);

CREATE INDEX IF NOT EXISTS math_rag_knowledge_source_idx
ON math_rag_knowledge (source);

-- 4. 创建知识库相似度搜索函数（1536维）
CREATE OR REPLACE FUNCTION search_knowledge(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.75,
    match_count int DEFAULT 5,
    filter_grade text DEFAULT NULL,
    filter_topic text DEFAULT NULL,
    filter_type text DEFAULT NULL,
    filter_difficulty text DEFAULT NULL
)
RETURNS TABLE(
    id int,
    source text,
    content text,
    type text,
    grade text,
    topic text,
    difficulty text,
    metadata jsonb,
    similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        k.id,
        k.source,
        k.content,
        k.type,
        k.grade,
        k.topic,
        k.difficulty,
        k.metadata,
        1 - (k.embedding <=> query_embedding) AS similarity
    FROM math_rag_knowledge k
    WHERE 1 - (k.embedding <=> query_embedding) > match_threshold
        AND (filter_grade IS NULL OR k.grade = filter_grade)
        AND (filter_topic IS NULL OR k.topic = filter_topic)
        AND (filter_type IS NULL OR k.type = filter_type)
        AND (filter_difficulty IS NULL OR k.difficulty = filter_difficulty)
    ORDER BY k.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 5. 创建添加知识的函数（1536维）
CREATE OR REPLACE FUNCTION add_knowledge(
    p_source text,
    p_content text,
    p_type text,
    p_grade text,
    p_topic text,
    p_difficulty text,
    p_embedding vector(1536),
    p_metadata jsonb DEFAULT NULL
)
RETURNS int
LANGUAGE plpgsql
AS $$
DECLARE
    new_id int;
BEGIN
    INSERT INTO math_rag_knowledge (source, content, type, grade, topic, difficulty, embedding, metadata)
    VALUES (p_source, p_content, p_type, p_grade, p_topic, p_difficulty, p_embedding, p_metadata)
    RETURNING id INTO new_id;

    RETURN new_id;
END;
$$;

-- 6. 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_math_rag_knowledge_updated_at
    BEFORE UPDATE ON math_rag_knowledge
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 验证配置信息
SELECT 'K12教育RAG AI聊天项目数据库初始化完成' as status;
SELECT '支持向量检索和教育内容分类存储' as description;
SELECT '向量维度: 1536' as dimension;

-- 显示表结构和索引信息
\d math_rag_knowledge
\di math_rag_knowledge*;

-- 8. 示例查询（注释掉，仅供参考）
/*
-- 示例：搜索中学二年级代数相关内容
SELECT id, content, metadata, similarity
FROM search_knowledge(
    '[query_embedding]'::vector(1536),
    0.75,
    5,
    'Sec2',     -- 年级过滤
    'Algebra',  -- 主题过滤
    NULL,       -- 类型不限
    NULL        -- 难度不限
);

-- 示例：添加新的知识条目
SELECT add_knowledge(
    'Mathematics Textbook Chapter 5',  -- source
    'Q: 解方程 2x + 3 = 7 A: x = 2',   -- content
    '题目',                            -- type
    'Sec2',                           -- grade
    'Algebra',                        -- topic
    '简单',                           -- difficulty
    '[embedding_vector]'::vector(1536), -- embedding
    '{"chapter": 5, "page": 45}'::jsonb -- metadata
);
*/