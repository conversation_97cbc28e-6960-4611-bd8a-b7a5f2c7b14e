spring:
  application:
    name: ai-code-helper

  # 单一数据源配置 (PostgreSQL)
  datasource:
    # PostgreSQL 数据源 (统一数据源)
    postgresql:
      url: **********************************************
      username: myappuser
      password: mypassword
      driver-class-name: org.postgresql.Driver
      hikari:
        pool-name: PostgreSQLPool
        maximum-pool-size: 15
        minimum-idle: 3
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

  # JPA 配置
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update  # 开发环境使用 update，生产环境建议使用 validate
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        # PostgreSQL 特定配置 (主数据源)
        dialect: org.hibernate.dialect.PostgreSQLDialect

server:
  port: 8081
  servlet:
    context-path: /api

# 向量数据库配置
embedding:
  # 模型类型: 使用ollama模型
  model-type: gemini
  # 数据库类型: postgresql (使用pgvector)
  store-type: postgresql
  # 本地模型配置 (BAAI/bge-m3)
  local:
    base-url: http://************:9000
    model-name: BAAI/bge-base-en-v1.5
    dimension: 768
  # Ollama模型配置 (dengcao/Qwen3-Embedding-0.6B:Q8_0)
  ollama:
    base-url: http://localhost:11434
    model-name: dengcao/Qwen3-Embedding-0.6B:Q8_0
    dimension: 1024
  # Gemini嵌入模型配置
  gemini:
    api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
    model-name: gemini-embedding-001
    dimension: 1536

# 聊天记忆配置
chat:
  memory:
    # 记忆类型: message（消息窗口） 或 token（令牌窗口）
    type: message
    # 最大消息数量（消息窗口模式）
    max-messages: 20
    # 最大令牌数量（令牌窗口模式）
    max-tokens: 1000
    # 调试模式
    debug-mode: true

# LangChain4j 配置 - 多模型配置
langchain4j:
  google:
    ai:
      gemini:
        # 默认聊天模型 (保持兼容性)
        chat-model:
          api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
          model-name: gemini-2.5-flash-lite-preview-06-17
        
        # 多模型配置 - 根据使用场景选择
        models:
          # 轻量级聊天模型 (平时聊天)
          lite:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-flash-lite
            description: "轻量级模型，用于日常聊天"
            temperature: 0.7
            max-tokens: 4096
          
          # RAG专用模型
          rag:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-flash-lite
            description: "RAG检索增强生成专用模型"
            temperature: 0.3
            max-tokens: 8192
          
          # 词典工具专用模型
          dictionary:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-flash
            description: "剑桥词典查询专用模型"
            temperature: 0.1
            max-tokens: 2048
          
          # 专业模型 (高级需求)
          pro:
            api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8
            model-name: gemini-2.5-pro
            description: "专业模型，用于复杂任务和高级需求"
            temperature: 0.2
            max-tokens: 16384
  
  # Ollama本地模型配置
  ollama:
    chat-model:
      base-url: http://localhost:11434
      model-name: gemma3n:e4b
      temperature: 0.7
      max-tokens: 16384

# BigModel API Key (如果需要)
bigmodel:
  api-key: AIzaSyBA79XSUkuVIo1plKKkHwmuAZLVUlOzcD8

# 日志配置
logging:
  level:
    com.hujun.aicodehelper.chat: DEBUG
    com.hujun.aicodehelper.ai: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.transaction: DEBUG
    
# 数据库配置说明
database:
  architecture:
    description: "单一数据库架构 - PostgreSQL存储聊天记忆和向量数据"
    postgresql:
      purpose: "聊天记忆、会话管理和向量存储"
      extensions: ["pgvector"]
      tables: ["chat_messages", "embeddings"]

# RAG配置
rag:
  # JSON文件处理配置
  json:
    # 是否启用JSON处理功能
    enabled: true
    # JSON文件源目录列表
    source-paths:
      - "data/cambridge/2025-06-28/output_json_standalone"
      - "data/myelt/2025-06-26/metadata_json"
    # 包含文件模式列表
    include-patterns:
      - "**/*.json"
    # 排除文件模式列表
    exclude-patterns:
      - "**/temp/**"
      - "**/.DS_Store"
      - "**/node_modules/**"
    # 最大文件大小限制（字节），默认10MB
    max-file-size: 10485760
    # 处理超时时间（秒），默认5分钟
    processing-timeout: 300
    # 是否启用并行处理
    parallel-processing: true
    # 线程池大小
    thread-pool-size: 4

# 语音合成配置 - 使用本地 Ollama Gemma 3n E4B 模型替代 Azure 语音服务
# 说明/假设:
# - 本地 Ollama 服务可通过 http://localhost:11434 访问（与代码库中其它 Ollama 配置一致）
# - 本地模型名为 gemma3n:e4b
# - 如果后端代码期望不同的配置结构，请告诉我，我会同时更新代码引用
speech:
  # 当前 TTS 提供者: 取值示例: azure | ollama
  provider: ollama

  # 文本到语音配置
  tts:
    provider: ollama
    ollama:
      # 本地 Ollama 服务地址（如需改为远程或不同端口，请修改）
      base-url: http://localhost:11434
      # 本地模型名称
      model-name: gemma3n:e4b
      # 温度/随机性，可按需调整
      temperature: 0.7
      # 输出音频格式（假设后端/前端支持 wav）
      format: wav
      sample-rate: 24000
      # 输出编码 (示例: pcm16)，根据播放/存储需求调整
      output-encoding: pcm16

  # 语音合成输出配置（保留原有目录设置）
  output:
    directory: "./data/speech-output"